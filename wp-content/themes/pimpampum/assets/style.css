/**************************
INDEX
***************************
1. PPP - Estuctura
2. PPP - Generals
3. PPP - Header
4. PPP - Home
5. PPP - Bloc Intro Pàgines
6. PPP - Teasers
7. PPP - Bloc Relacionats
8. PPP - Llistat Àrees - Serveis
9. PPP - Llistat Articles i Notícies
10. PPP - Single Notícia / Article
11. PPP - Single Equip
12. PPP - Single Àrea
13. Contacte
14. Footer
15. Newsletter
16. Pagination
17. Patrons
***************************/

:root {
	--mainfont: "PPNeueMontreal", Helvetica, arial, sans-serif;

	/* Mides */
	--gapXXS: 8px;
	--gapXS: 12px;
	--gapS: 16px;
	--gapM: 24px;
	--gapL: 32px;
	--gapXL: 48px;
	--gapXXL: 64px;
	--gap-page-top: 64px;
	--gap-page-bottom: 120px;
	--radius: 8px;
	--radiusM: 8px;
	--radiusL: 20px;
	--border: 1.5px solid var(--Brand-Black);
	--border-thin: 1px solid var(--Brand-Black);

	/* Colors */
	--bgpage: #f7f6f2;
	--Brand-Black: #3c3f36;
	--Brand-Black-50: rgba(60, 63, 54, 0.5);
	--Brand-Black-85: #3c3f36d9;
	--yellow: #fbf348;
	--white: #ffffff;
	--white-50: #ffffff80;
	--white-20: #ffffff33;
	--light-grey: #f7f6f2;
	--medium-grey: #e5e4df;
	--dark-grey: #d3d0c7;
	--pure-black: #000000;
	--soft-bg: var(--light-grey);
	--white-bg: var(--white);
	--dark-bg: var(--black);
	--yellow-bg: var(--yellow);
	--overlay: #131411e5;

	/* Tipografia */

	--h1: 2.625rem;
	--h1-lh: 1.05;
	--h1-ls: 0.01em;

	--h2: 2.125rem;
	--h2-lh: 1.02;
	--h2-ls: 0.01em;

	--h3: 1.75rem;
	--h3-lh: 1.11;
	--h3-ls: -0.03em;

	--h3-nws: 1.75rem;
	--h3-nws-lh: 1.1;
	--h3-nws-ls: 0.01em;

	--h4: 1.625rem;
	--h4-lh: 1.1;
	--h4-ls: 0;

	--h5: 1.5rem;
	--h5-lh: 1.18;
	--h5-ls: 0.01em;

	--h6: 1rem;
	--h6-lh: 1.2;
	--h6-ls: 0.01em;

	--h7: 1.125rem;
	--h7-lh: 1.25;
	--h7-ls: 0;

	--h8: 1.125rem;
	--h8-lh: 1.25;
	--h8-ls: 0.01em;

	--h9: 0.875rem;
	--h9-lh: 1.3;
	--h9-ls: 0.01em;

	--txt-xl: 1.25rem;
	--txt-xl-lh: 1.25;
	--txt-xl-ls: 0;

	--txt-lg: 1.125rem;
	--txt-lg-lh: 1.25;
	--txt-lg-ls: 0;

	--txt-md: 1rem;
	--txt-md-lh: 1.3;
	--txt-md-ls: 0.01em;

	--txt-sm: 0.875rem;
	--txt-sm-lh: 1.35;
	--txt-sm-ls: 0;

	--txt-xs: 0.75rem;
	--txt-xs-lh: 1.35;
	--txt-xs-ls: 0;

	--txt-xs-italic: 0.75rem;
	--txt-xs-italic-lh: 1.35;
	--txt-xs-italic-ls: 0;

	--txt-xs-bold: 0.75rem;
	--txt-xs-bold-lh: 1.35;
	--txt-xs-bold-ls: 0;

	--txt-xxs: 0.625rem;
	--txt-xxs-lh: 1.35;
	--txt-xxs-ls: 0;

	--quote-ficha: 1.375rem;
	--quote-ficha-lh: 1.15;
	--quote-ficha-ls: 0;

	--nombre-ficha: 1.25rem;
	--nombre-ficha-lh: 1.12;
	--nombre-ficha-ls: 0;

	--tag: 0.75rem;
	--tag-lh: 1.2;
	--tag-ls: 0.01em;

	--fecha: 0.75rem;
	--fecha-lh: 1.2;
	--fecha-ls: 0.01em;

	--categorias-home: clamp(1.25rem, 4.58vw, 5.5rem);
}

@media (min-width: 768px) {
	:root {
		--h1: 4.25rem;
		--h1-lh: 0.96;
		--h1-ls: 0.01em;

		--h2: 3rem;
		--h2-lh: 1;
		--h2-ls: 0.01em;

		--h3: 2.75rem;
		--h3-lh: 1.15;
		--h3-ls: -0.03em;

		--h3-nws: 2.75rem;
		--h3-nws-lh: 1.05;
		--h3-nws-ls: 0.01em;

		--h4: 2.5rem;
		--h4-lh: 1.05;
		--h4-ls: 0;

		--h5: 2.25rem;
		--h5-lh: 1.25;
		--h5-ls: 0.01em;

		--h6: 2.25rem;
		--h6-lh: 1.25;
		--h6-ls: 0.01em;

		--h7: 1.75rem;
		--h7-lh: 1.2;
		--h7-ls: 0;

		--h8: 1.75rem;
		--h8-lh: 1.25;
		--h8-ls: 0.01em;

		--h9: 0.875rem;
		--h9-lh: 1.3;
		--h9-ls: 0.01em;

		--txt-xl: 2.25rem;
		--txt-xl-lh: 1.3;
		--txt-xl-ls: 0;

		--txt-lg: 2rem;
		--txt-lg-lh: 1.25;
		--txt-lg-ls: 0;

		--txt-md: 1.5rem;
		--txt-md-lh: 1.35;
		--txt-md-ls: 0.01em;

		--txt-sm: 1.125rem;
		--txt-sm-lh: 1.35;
		--txt-sm-ls: 0;

		--txt-xs: 0.875rem;
		--txt-xs-lh: 1.35;
		--txt-xs-ls: 0;

		--txt-xs-italic: 0.875rem;
		--txt-xs-italic-lh: 1.35;
		--txt-xs-italic-ls: 0;

		--txt-xs-bold: 0.875rem;
		--txt-xs-bold-lh: 1.35;
		--txt-xs-bold-ls: 0;

		--txt-xxs: 0.75rem;
		--txt-xxs-lh: 1.2;
		--txt-xxs-ls: 0;

		--quote-ficha: 2.25rem;
		--quote-ficha-lh: 1.15;
		--quote-ficha-ls: 0;

		--nombre-ficha: 2rem;
		--nombre-ficha-lh: 1.12;
		--nombre-ficha-ls: 0;

		--tag: 0.75rem;
		--tag-lh: 1;
		--tag-ls: 0.01em;

		--fecha: 1.125rem;
		--fecha-lh: 1;
		--fecha-ls: 0.01em;
	}
}

@media (min-width: 1280px) {
	:root {
		/* Mides */
		--radius: 8px;
		--radiusM: 16px;
		--radiusL: 24px;
		--border: 1.5px solid var(--Brand-Black);
		--gapXXS: 10px;
		--gapXS: 16px;
		--gapS: 24px;
		--gapM: 32px;
		--gapL: 48px;
		--gapXL: 64px;
		--gapXXL: 80px;
		--gap-page-top: 160px;
		--gap-page-bottom: 220px;

		--dropwdown: -4px 4px 4px 0px rgba(0, 0, 0, 0);

		--h1: clamp(4.5rem, 5.417vw, 6.5rem);
		--h1-lh: 0.96;
		--h1-ls: 0.01em;

		--h2: clamp(2.75rem, 3.333vw, 4rem);
		--h2-lh: 1;
		--h2-ls: 0.01em;

		--h3: clamp(2.625rem, 3.125vw, 3.75rem);
		--h3-lh: 1.15;
		--h3-ls: -0.03em;

		--h3-nws: clamp(2.5rem, 2.813vw, 3.375rem);
		--h3-nws-lh: 1.05;
		--h3-nws-ls: 0.01em;

		--h4: clamp(1.875rem, 2.188vw, 2.625rem);
		--h4-lh: 1.05;
		--h4-ls: 0;

		--h5: clamp(1.5rem, 1.771vw, 2.125rem);
		--h5-lh: 1.25;
		--h5-ls: 0.01em;

		--h6: clamp(1.25rem, 1.458vw, 1.75rem);
		--h6-lh: 1.25;
		--h6-ls: 0.01em;

		--h7: clamp(1.125rem, 1.25vw, 1.5rem);
		--h7-lh: 1.2;
		--h7-ls: 0;

		--h8: clamp(0.875rem, 1.042vw, 1.25rem);
		--h8-lh: 1.25;
		--h8-ls: 0.01em;

		--h9: clamp(0.875rem, 1.042vw, 1.25rem);
		--h9-lh: 1.3;
		--h9-ls: 0.01em;

		--txt-xl: clamp(1.375rem, 1.458vw, 1.75rem);
		--txt-xl-lh: 1.3;
		--txt-xl-ls: 0;

		--txt-lg: clamp(1.25rem, 1.458vw, 1.75rem);
		--txt-lg-lh: 1.25;
		--txt-lg-ls: 0;

		--txt-md: clamp(1.125rem, 1.25vw, 1.5rem);
		--txt-md-lh: 1.35;
		--txt-md-ls: 0.01em;

		--txt-sm: clamp(0.875rem, 1.042vw, 1.25rem);
		--txt-sm-lh: 1.35;
		--txt-sm-ls: 0;

		--txt-xs: clamp(0.75rem, 0.833vw, 1rem);
		--txt-xs-lh: 1.35;
		--txt-xs-ls: 0;

		--txt-xs-italic: clamp(0.75rem, 0.833vw, 1rem);
		--txt-xs-italic-lh: 1.35;
		--txt-xs-italic-ls: 0;

		--txt-xs-bold: clamp(0.75rem, 0.833vw, 1rem);
		--txt-xs-bold-lh: 1.35;
		--txt-xs-bold-ls: 0;

		--txt-xxs: clamp(0.75rem, 0.729vw, 0.875rem);
		--txt-xxs-lh: 1.2;
		--txt-xxs-ls: 0;

		--quote-ficha: clamp(2.25rem, 2.604vw, 3.125rem);
		--quote-ficha-lh: 1.15;
		--quote-ficha-ls: 0;

		--nombre-ficha: clamp(1.75rem, 2.083vw, 2.5rem);
		--nombre-ficha-lh: 1.12;
		--nombre-ficha-ls: 0;

		--tag: clamp(0.625rem, 0.72vw, 0.875rem);
		--tag-lh: 1;
		--tag-ls: 0.01em;

		--fecha: clamp(0.75rem, 0.729vw, 0.875rem);
		--fecha-lh: 1.2;
		--fecha-ls: 0.01em;
	}
}

/**************************
PPP - Estructura
***************************/

body,
button,
input,
select,
textarea {
	font-family: var(--mainfont);
	font-size: var(--txt-md);
	line-height: var(--txt-md-lh);
	letter-spacing: var(--txt-md-ls);
	font-weight: 400;
	background-color: var(--bgpage);
	color: var(--Brand-Black);
}

body.menu-open {
	overflow: hidden;
}

.wrap {
	width: 90%;
	max-width: 1792px;
	margin-left: auto;
	margin-right: auto;
	position: relative;
}

.wrap1000 {
	max-width: 1000px;
}

main {
	padding-top: var(--gap-page-top);
}

main:has(.grid-equip),
main:has(.contacte-form),
.single-categoria_terraqui_a main,
main.error-404-page {
	padding-bottom: var(--gap-page-bottom);
}

/**************************
PPP - GENERALS
***************************/

a,
a:visited {
	color: var(--Brand-Black);
	text-decoration: none;
	text-underline-offset: 0.2em;
	text-decoration-thickness: 1px;
}

a:hover,
.post-content a,
.post-content a:visited {
	text-decoration: underline;
}

h1 {
	font-size: var(--h2);
	line-height: var(--h2-lh);
	letter-spacing: var(--h2-ls);
}

blockquote {
	quotes: "\00201C""\00201D""\002018""\002019";
}
blockquote::before {
	content: open-quote;
}
blockquote::after {
	content: close-quote;
}

/* Botons */

p.boto {
	margin: 0;
}

.boto a,
.contacte-info a[href*="maps"],
.contacte-form input[type="submit"],
button.apply-filters-btn,
.klaro .cn-button,
#klaro-modal .cm-button,
.carousel-home .carousel-text a {
	color: var(--Brand-Black);
	font-size: var(--txt-xl);
	line-height: var(--txt-xl-lh);
	letter-spacing: var(--txt-xl-ls);
	white-space: nowrap;
	padding: 8px 16px;
	border: var(--border);
	border-radius: 50px;
	display: inline-block;
	background-color: transparent;
	transition: background-color 0.3s;
}

:is(.boto a,.contacte-info a[href*="maps"],.contacte-form input[type="submit"],button.apply-filters-btn,.klaro .cn-button,#klaro-modal .cm-button,.carousel-home .carousel-text a):hover {
		background-color: var(--Brand-Black);
		color: var(--bgpage);
		text-decoration: none;
		transition: background-color 0.3s;
	}

a.wp-element-button {
	background-color: var(--Brand-Black);
	color: var(--bgpage);
	border: var(--border);
	padding: 6px 16px 8px 16px;
	font-size: var(--txt-sm);
	line-height: var(--txt-sm-lh);
	letter-spacing: var(--txt-sm-ls);
	text-decoration: none;
}

a.wp-element-button:hover {
		background-color: var(--bgpage);
		color: var(--Brand-Black);
		border: var(--border);
	}

a.wp-element-button:hover::after {
		filter: brightness(1000%);
	}

.recursos .boto a,
.boto.ver-mas a,
.contacte-info a[href*="maps"],
.carousel-home .carousel-text a,
.boto-newsletter a {
	display: flex;
	gap: 8px;
	padding-right: 11px;
	justify-content: space-between;
	align-items: center;
}

:is(.recursos .boto a,.boto.ver-mas a,.contacte-info a[href*="maps"],.carousel-home .carousel-text a,.boto-newsletter a)::after {
		content: " ";
		background: url("../img/ico_arrow_down_mobile.svg") no-repeat center;
		width: 20px;
		height: 20px;
		background-size: contain;
	}

:is(.recursos .boto a,.boto.ver-mas a,.contacte-info a[href*="maps"],.carousel-home .carousel-text a,.boto-newsletter a):hover::after {
		filter: brightness(1000%);
	}

.recursos .boto a {
	font-size: var(--txt-lg);
	line-height: var(--txt-lg-lh);
	letter-spacing: var(--txt-lg-ls);
	text-wrap: wrap;
}

.recurso-web-link:is(.recursos .boto a)::after {
		transform: rotate(-135deg);
	}

.boto.ver-mas a {
	background-color: var(--Brand-Black);
	color: var(--bgpage);
}

:is(.boto.ver-mas a)::after {
		filter: brightness(1000%);
		transform: rotate(-135deg);
		transition: all 0.3s;
	}

.carousel-home .carousel-text a::after,
.boto-newsletter a::after {
	transform: rotate(-135deg);
}

.contacte-info a[href*="maps"] {
	display: inline-flex;
	gap: 5px;
	margin-top: var(--gapXS);
	font-size: var(--txt-md);
	line-height: var(--txt-md-lh);
	letter-spacing: var(--txt-md-ls);
	padding: 6px 8px 6px 11px;
}

:is(.contacte-info a[href*="maps"]):hover {
		background-color: var(--bgpage);
		color: var(--Brand-Black);
	}

:is(.contacte-info a[href*="maps"])::after {
		width: 14px;
		height: 14px;
		margin-left: 0;
		background-size: contain;
	}

:is(.contacte-info a[href*="maps"]):hover::after {
		filter: none;
	}

/* FORMULARIS */

input[type="text"],
input[type="email"],
textarea {
	border: none;
	border-radius: 0;
	padding: 0 0 14px 0;
	width: 100%;
	line-height: 1.35;
	color: var(--Brand-Black);
	border-bottom: 1px solid var(--Brand-Black);
}

:is(input[type="text"],input[type="email"],textarea):focus {
		outline: none;
	}

:is(input[type="text"],input[type="email"],textarea)::-moz-placeholder {
		color: var(--Brand-Black);
	}

:is(input[type="text"],input[type="email"],textarea)::placeholder {
		color: var(--Brand-Black);
	}

input[type="submit"] {
	padding: 13px var(--gapS);
}

input[type="submit"]::after {
		filter: brightness(1000%);
	}

input[type="submit"]:hover {
		background-color: var(--bgpage);
		color: var(--Brand-Black);
		cursor: pointer;
	}

input[type="submit"]:hover::after {
		filter: brightness(1000%);
	}

.wpcf7-not-valid-tip {
	font-size: var(--txt-xs);
	line-height: 1.2;
	letter-spacing: var(--txt-xs-ls);
	font-weight: 700;
	margin-top: 6px;
}

/* Amaguem recaptcha */

.grecaptcha-badge {
	visibility: hidden;
}

/**************************
PPP - HEADER
***************************/

#masthead .wrap {
	padding-top: 30px;
	padding-bottom: 13px;
}

.site-branding {
	max-width: 177px;
}

body:has(nav.toggled) {
	overflow: hidden;
}

body:has(nav.toggled) #masthead {
		width: 100vw;
		height: 100dvh;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 99;
		margin: 0;
		background-color: var(--yellow);
	}

:is(body:has(nav.toggled) #masthead) .wrap {
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			padding-bottom: 50px;
		}

/**************************
PPP - HOME
***************************/

.top-home {
	background: url("../img/textura-home.jpg") no-repeat center;
	background-size: cover;
	margin-top: -104px;
	padding-top: calc(200px + 40px);
	padding-bottom: var(--gapL);
}

.top-home h2 {
		max-width: 1200px;
		font-size: var(--h1);
		line-height: var(--h1-lh);
		letter-spacing: var(--h1-ls);
		font-weight: var(--h1-fw);
		text-wrap: balance;
	}

.site-branding {
	z-index: 3;
}

.home main {
	padding-top: 0;
}

.carousel-home-container {
	position: relative;
	background-color: #fff;
}

:is(:is(.carousel-home-container .carousel-item) .carousel-image) img {
				width: 100%;
				height: 40vh;
				-o-object-fit: cover;
				   object-fit: cover;
				transition: opacity 0.8s linear;
			}

.carousel-home-container .slick-slide {
		opacity: 0;
		transition: opacity 0.8s linear;
	}

.carousel-home-container .slick-active {
		opacity: 1;
	}

.carousel-home-container .carousel-content {
		font-size: var(--h5);
		line-height: var(--h5-lh);
		letter-spacing: var(--h5-ls);
		padding: clamp(20px, 9.5vw, 184px) clamp(5%, 6.77vw, 130px) clamp(80px, 9.5vw, 184px) clamp(5%, 6.77vw, 130px);

		padding: clamp(20px, 9.5vw, 184px) clamp(5%, 6.77vw, 130px) 80px clamp(5%, 6.77vw, 130px);
		min-height: 320px;
		transition: opacity 0.6s linear;
	}

:is(.carousel-home-container .carousel-content) p {
			margin: 0;
		}

.carousel-home-container .carousel-text p:has(a) {
		display: flex;
		margin-top: 20px !important;
	}

.carousel-home-container .slide-count {
		display: none;
	}

.carousel-home-container .slick-arrow {
		display: block;
		width: 23px;
		height: 18px;
		text-indent: -999em;
		position: absolute;
		top: 35vh;
		left: auto;
		z-index: 10;
		filter: brightness(1000%);
	}

:is(.carousel-home-container .slick-arrow)::before {
			content: none;
		}

.slick-next:is(.carousel-home-container .slick-arrow) {
			background: url(../img/ico_arrow_next_mobile.svg) no-repeat center;
			background-size: contain;
			right: 5%;
			transform: none;
		}

.slick-prev:is(.carousel-home-container .slick-arrow) {
			background: url(../img/ico_arrow_prev_mobile.svg) no-repeat center;
			background-size: contain;
			left: 5%;
			transform: none;
		}

#home-slogan {
	background-color: var(--bgpage);
	font-size: var(--h2);
	line-height: var(--h2-lh);
	letter-spacing: var(--h2-ls);
	padding: 40px 0 32px 0;
	text-wrap: balance;
}

#home-slogan p {
		margin-bottom: 0;
	}

.bloc-relacionats .tns-outer {
		display: flex;
		flex-direction: column;
		width: 100%;
	}

.bloc-relacionats #tns1 > .tns-item {
		padding-left: 5vw;
		padding-right: 0;
	}

.bloc-relacionats .tns-nav {
		order: 10;
		display: flex;
		gap: 5px;
		justify-content: center;
		align-items: center;
		margin-top: 30px;
	}

:is(.bloc-relacionats .tns-nav) button {
			border: 1px solid var(--Brand-Black);
			border-radius: 50px;
			width: 8px;
			height: 8px;
			font-size: 1px;
		}

.tns-nav-active:is(:is(.bloc-relacionats .tns-nav) button) {
				background-color: var(--Brand-Black);
				width: 12px;
				height: 12px;
			}

:is(:is(.bloc-relacionats .tns-nav) button):hover {
				background-color: var(--Brand-Black);
			}

:is(:is(.bloc-relacionats .tns-nav) button):focus {
				outline: none;
			}

:is(:is(.bloc-relacionats .tns-nav) button):focus-visible {
				outline: none;
			}

.bloc-relacionats .grid-teasers {
	width: 90%;
	margin-left: auto;
	margin-right: auto;
	position: relative;
}

/**************************
PPP - BLOC INTRO PÀGINES
***************************/

.pagina-excerpt {
	max-width: 900px;
	font-size: var(--h2);
	line-height: 1.2;
	letter-spacing: var(--h2-ls);
	text-wrap: balance;
}

/**************************
PPP - TEASERS
***************************/

/* Teaser grid general */

.grid-teasers {
	display: grid;
	grid-template-columns: 1fr;
	gap: var(--gapL);
}

.bloc-relacionats .grid-teasers .teaser-article:nth-child(n + 5) {
	display: none;
}

.home .grid-teasers {
	display: block;
}

/* Teaser notícies */

.teaser-article {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.teaser-article .post-categories {
		margin-top: auto;
	}

.teaser-media {
	position: relative;
	border-radius: var(--radiusL) 0 var(--radiusL) 0;
	overflow: hidden;
	transition: all 0.3s;
	margin-bottom: var(--gapXXS);
}

.teaser-tipus {
	position: absolute;
	top: var(--gapXXS);
	left: var(--gapXXS);
	background-color: #fff;
	border-radius: 50px;
	line-height: 1;
}

.teaser-tipus a {
	font-size: var(--tag);
	line-height: 1.2;
	letter-spacing: var(--tag-ls);
	font-weight: var(--tag-fw);
	display: flex;
	padding: 7px 10px 6px 10px;
	text-transform: uppercase;
	background-color: #fff;
	border-radius: 50px;
	background-position: -37px -16px;
}

:is(.teaser-tipus a):hover {
		text-decoration: none;
	}

.teaser-article:not(.no-thumbnail) .teaser-tipus a {
	color: rgba(0, 0, 0, 0.3);
	-webkit-background-clip: text;
	        background-clip: text;
	text-fill-color: transparent;
}

.teaser-article.no-thumbnail .teaser-tipus a {
	background-image: none !important;
	background-color: var(--Brand-Black);
	color: #fff;
}

.teaser-llegir-mes {
	position: absolute;
	right: var(--gapXXS);
	top: var(--gapXXS);
}

.teaser-llegir-mes a {
		width: 36px;
		height: 36px;
		display: block;
		text-indent: -999em;
		background: url(../img/veure-mes-icon.svg) no-repeat center;
		background-size: contain;
		opacity: 0;
		transition: opacity 0.3s;
	}

.teaser-article:hover .teaser-media {
		border-radius: var(--radiusL);
		transition: all 0.3s;
	}

.teaser-article:hover .teaser-llegir-mes a {
		opacity: 1;
		transition: opacity 0.3s;
	}

h2.teaser-titol {
	font-size: var(--h7);
	line-height: var(--h7-lh);
	letter-spacing: var(--h7-ls);
	margin-bottom: 8px;
}

.no-thumbnail .teaser-llegir-mes a {
		filter: invert(1);
	}

.no-thumbnail .teaser-media a[rel="bookmark"]::after {
		content: url("../img/logo_terraqui_teasers.svg");
		position: absolute;
		right: var(--gapM);
		bottom: var(--gapM);
	}

/* Teaser equip */

.grid-teasers.grid-equip {
	display: grid;
	grid-template-columns: 1fr 1fr;
	-moz-column-gap: var(--gapXS);
	     column-gap: var(--gapXS);
	row-gap: var(--gapM);
}

.teaser-equip {
	position: relative;
}

.teaser-equip img {
		border-radius: var(--radiusM);
		margin-bottom: var(--gapXS);
	}

.teaser-equip .teaser-equip-info {
		display: flex;
		flex-direction: column;
		gap: 12px;
	}

:is(.teaser-equip .teaser-equip-info) h2 {
			font-size: var(--nombre-ficha);
			line-height: var(--nombre-ficha-lh);
			letter-spacing: var(--nombre-ficha-ls);
		}

:is(:is(.teaser-equip .teaser-equip-info) h2) a:hover {
				text-decoration: none;
			}

:is(:is(.teaser-equip .teaser-equip-info) h2) a::before {
				content: "";
				position: absolute;
				top: 0px;
				right: 0px;
				bottom: 0px;
				left: 0px;
			}

.teaser-equip .teaser-equip-over {
		display: none;
	}

/* Teaser publicacions */

body[class*="page-publicaciones"] main {
		padding-bottom: var(--gap-page-bottom);
	}

body[class*="page-publicaciones"] .grid-teasers {
		gap: var(--gapXS);
	}
.teaser-article.publicacion {
	display: grid;
	grid-template-columns: 1fr;
	background-color: var(--Brand-Black);
	background-size: cover;
	color: var(--yellow);
	border-radius: 32px 0 32px 0;
	overflow: hidden;
}
.teaser-article.publicacion a,.teaser-article.publicacion a:visited {
		color: var(--yellow);
	}
.teaser-article.publicacion .teaser-publicacion-info {
		grid-column: 1/2;
		grid-row: 1/2;
		padding: var(--gapS);
		z-index: 2;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		row-gap: var(--gapL);
	}
.teaser-article.publicacion .teaser-publicacion-info-top {
		display: grid;
		grid-template-columns: 1fr 41px;
		gap: var(--gapXXS);
	}
.teaser-article.publicacion h2.teaser-titol {
		margin-bottom: 4px;
	}
.teaser-article.publicacion .teaser-publicacion-media {
		grid-column: 1/2;
		grid-row: 1/2;
		z-index: 1;
		width: 100%;
		height: 100%;
		opacity: 0;
		background-repeat: no-repeat;
		background-size: cover;
	}
.teaser-article.publicacion .post-categories {
		margin-top: 0;
	}
:is(.teaser-article.publicacion .post-categories) a {
			border-color: var(--yellow);
		}
:is(.teaser-article.publicacion .publicacion-download) p {
			margin: 0;
		}
:is(.teaser-article.publicacion .publicacion-download) a {
			width: 33px;
			height: 33px;
			display: block;
			text-indent: -999em;
			background: url("../img/ico_download.svg") no-repeat center;
			background-size: contain;
		}
.teaser-article.publicacion:hover {
		color: var(--Brand-Black);
	}
.teaser-article.publicacion:hover .teaser-publicacion-media {
			opacity: 1;
			transition: opacity 0.3s;
		}
.teaser-article.publicacion:hover a,.teaser-article.publicacion:hover a:visited {
			color: var(--Brand-Black);
		}
.teaser-article.publicacion:hover .post-categories a {
			border-color: var(--Brand-Black);
		}
:is(.teaser-article.publicacion:hover .post-categories a):hover {
				color: var(--yellow);
			}

/**************************
PPP - BLOC RELACIONATS
***************************/

.bloc-relacionats {
	padding: var(--gap-page-top) 0 var(--gap-page-bottom) 0;
}

.bloc-relacionats-header {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	gap: var(--gapS);
	margin-bottom: var(--gapM);
}

.bloc-relacionats-header h2 {
		font-size: var(--h3);
		line-height: var(--h3-lh);
		letter-spacing: var(--h3-ls);
		max-width: 1300px;
		text-wrap: balance;
		text-transform: lowercase;
	}

:is(.bloc-relacionats-header h2)::first-letter {
			text-transform: uppercase;
		}

.bloc-relacionats-header .boto.ver-mas a {
		text-indent: -999em;
		background-color: transparent;
		border: none;
	}

:is(.bloc-relacionats-header .boto.ver-mas a):hover {
			background-color: transparent !important;
		}

:is(.bloc-relacionats-header .boto.ver-mas a):after {
			filter: brightness(0);
		}

.home .bloc-relacionats {
	padding-bottom: 72px;
}

#area-contingut .bloc-relacionats {
	padding-bottom: 0;
}

/**************************
PPP - LLISTAT ÀREES - SERVEIS
***************************/

.servicios-lista {
	background-color: #fff;
	padding: var(--gap-page-top) 0 var(--gap-page-bottom) 0;
}

.servicios-lista h2 {
		font-size: var(--h2);
		line-height: var(--h2-lh);
		letter-spacing: var(--h2-ls);
		max-width: 900px;
		text-wrap: balance;
		margin-bottom: 48px;
	}

.servicios-lista ol {
		font-size: var(--txt-xl);
		line-height: 1.05;
		letter-spacing: 2%;
		counter-reset: item;
		list-style-type: none;
	}

:is(.servicios-lista ol) li {
			counter-increment: item;
			position: relative;
			padding-top: 4px;
			padding-bottom: 28px;

			border-top: 1px solid var(--Brand-Black-50);
		}

:is(:is(.servicios-lista ol) li):last-of-type {
				padding-bottom: 0;
			}

:is(:is(.servicios-lista ol) li)::before {
				content: counter(item, decimal-leading-zero) ". ";
				position: absolute;
				left: 0;
			}

:is(.servicios-lista ol) a {
			padding-right: 40px;
			padding-left: 2.6em;
			background: url("../img/ico_arrow_diagonal.svg") no-repeat calc(100% - 4px) 5px;
			background-size: 13px auto;
			display: block;
		}

:is(.servicios-lista ol) a:hover {
			text-decoration: none;
		}

/**************************
PPP - LLISTAT ARTICLES I NOTÍCIES
***************************/

body[class*="page-actualidad"] main {
		padding-bottom: var(--gap-page-bottom);
	}

.post-data {
	font-size: var(--fecha);
	line-height: var(--fecha-lh);
	letter-spacing: var(--fecha-ls);
	margin-bottom: 2px;
}

.post-categories ul {
	list-style-type: none;
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

:is(.post-categories ul) a,:is(.post-categories ul) a:visited {
		font-size: var(--txt-sm);
		line-height: var(--txt-sm-lh);
		letter-spacing: 0;
		padding: 6px 12px;
		border: var(--border-thin);
		border-radius: 50px;
		text-wrap: nowrap;

		display: block;
	}

:is(.post-categories ul) a:hover {
		background-color: var(--Brand-Black);
		color: var(--bgpage);
		text-decoration: none;
	}

/* Filtres */

.archive-filtres {
	display: grid;
	grid-template-columns: 1fr;
	gap: var(--gapXXS);
	margin-bottom: 40px;
	max-width: 356px;
}

.archive-filtres p {
		margin: 0;
	}

.search-results:is(.archive-filtres p) {
			margin-top: var(--gapM);
		}

.archive-filtres-grup {
	display: flex;
	flex-wrap: wrap;
	gap: var(--gapXXS);
}

.filtre-tipus {
	background: var(--medium-grey);
	border-radius: 100px;
	display: flex;
	gap: 4px;
	list-style-type: none;
	width: 100%;
}

:is(.filtre-tipus li) a,:is(.filtre-tipus li) a:visited {
			display: inline-block;
			padding: 14px 30px;
			border-radius: 100px;
			font-size: var(--txt-sm);
			line-height: var(--txt-sm-lh);
			letter-spacing: var(--txt-sm-ls);
			background-color: var(--medium-grey);
			transition: background-color 0.3s;
			text-align: center;
		}

:is(.filtre-tipus li) a:hover {
			background-color: var(--dark-grey);
			transition: background-color 0.3s;
			text-decoration: none;
		}

.selected:is(.filtre-tipus li) a,.selected:is(.filtre-tipus li) a:visited {
			background-color: var(--Brand-Black);
			color: var(--bgpage);
			transition: background-color 0.3s;
		}

.filtre-recent {
	text-wrap: nowrap;
}

.filtre-recent a,.filtre-recent a:visited {
		display: inline-block;
		padding: 14px 24px;
		border-radius: 100px;
		font-size: var(--txt-sm);
		line-height: var(--txt-sm-lh);
		letter-spacing: var(--txt-sm-ls);
		background-color: var(--Brand-Black);
		color: var(--bgpage);
		transition: background-color 0.3s;
	}

.filtre-recent a:hover {
		text-decoration: none;
		background-color: var(--medium-grey);
		color: var(--Brand-Black);
		transition: background-color 0.3s, color 0.3s;
	}

.js-filtre-desplegable {
	position: relative;
	width: 100%;
	max-width: 420px;
}

.filtre-area-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: var(--gapXS);
	padding: 14px 20px;
	background: var(--medium-grey);
	border-radius: 100px;
	cursor: pointer;
	font-size: var(--txt-sm);
	line-height: var(--txt-sm-lh);
	letter-spacing: var(--txt-sm-ls);
}

.filtre-area-toggle {
	width: 14px;
	height: 14px;
	background: url(../img/ico_arrows.svg) no-repeat left -36px;
	background-size: 14px auto;
	transition: transform 0.3s;
}

.js-filtre-desplegable.desplegat .filtre-area-toggle {
	transform: rotate(180deg);
}

.filtre-area-content {
	display: none;
	position: absolute;
	width: 100%;
	max-width: 400px;
	background: var(--medium-grey);
	border-radius: 28px;
	margin-top: 5px;
	z-index: 100;
	padding: var(--gapS);
	font-size: var(--txt-sm);
	line-height: var(--txt-sm-lh);
	letter-spacing: var(--txt-sm-ls);
}

.filtre-area-list {
	list-style-type: none;
	overflow-y: auto;
	margin-bottom: 10px;
}

.filtre-area-item {
	padding: 5px 2px;
	display: flex;
	align-items: baseline;
}

.filtre-area-item input[type="checkbox"] {
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	width: 9px;
	height: 9px;
	aspect-ratio: 1;
	margin-right: 10px;
	border-radius: 20px;
	border: 1px solid var(--medium-grey);
	outline: 1px solid var(--Brand-Black);
}

.filtre-area-item input[type="checkbox"]:checked {
	background: var(--Brand-Black);
}

button.apply-filters-btn {
	display: block;
	width: 100%;
	font-size: var(--txt-sm);
	line-height: var(--txt-sm-lh);
	letter-spacing: var(--txt-sm-ls);
}

.filtres-aplicats {
	grid-column: 1/2;
	grid-row: 2/3;
	display: flex;
	flex-direction: row;
	gap: 8px;
	flex-wrap: wrap;
}

.filtres-aplicats li {
		display: flex;
		gap: 8px;
		align-items: center;
		padding: 10px 16px;
		border: var(--border-thin);
		border-radius: 100px;
		font-size: var(--txt-sm);
		letter-spacing: var(--txt-sm-ls);
		line-height: 1;
		text-wrap: nowrap;
	}

.filtres-aplicats a.filtre-eliminar {
		display: block;
		width: 16px;
		height: 16px;
		background: url(../img/ico_close.svg) no-repeat center;
		background-size: contain;
		text-indent: -999em;
	}

.archive-cerca form {
		position: relative;
	}

.archive-cerca input[type="search"] {
		background-color: var(--medium-grey);
		border-radius: 100px;
		border: none;
		font-size: var(--txt-sm);
		line-height: 1.2;
		color: var(--Brand-Black);
		width: 100%;

		padding: 15px 60px 15px 24px;
	}

:is(.archive-cerca input[type="search"])::-moz-placeholder {
			color: var(--Brand-Black-50);
		}

:is(.archive-cerca input[type="search"])::placeholder {
			color: var(--Brand-Black-50);
		}

:is(.archive-cerca input[type="search"]):focus {
			outline-color: #000;
		}

.archive-cerca button[type="submit"] {
		display: block;
		width: 22px;
		height: 22px;
		background: url(../img/ico_lupa.svg) no-repeat center;
		background-size: 20px auto;
		text-indent: -999em;
		position: absolute;
		right: 20px;
		top: 50%;
		transform: translateY(-50%);
		padding: 0;
		border: none;
	}

/**************************
PPP - SINGLE NOTÍCIA / ARTICLE
***************************/

.single-post main {
	padding-top: 0;
}

.single-header {
	display: flex;
	flex-direction: column;
	margin-top: var(--gapM);
	margin-bottom: 56px;
}

.single-header h1.post-titol {
		font-size: var(--h4);
		line-height: var(--h4-lh);
		letter-spacing: var(--h4-ls);
		margin-bottom: var(--gapS);
	}

.single-header .post-data {
		order: -1;
		margin-bottom: 4px;
		font-family: var(--mainfont);
		font-size: var(--txt-sm);
		line-height: var(--txt-sm-lh);
		letter-spacing: var(--txt-sm-ls);
	}

img.single-post-thumbnail {
	max-width: none;
	width: 100%;
	height: 30vh;
	max-height: 180px;
	-o-object-fit: cover;
	   object-fit: cover;
}

.single-content {
	display: flex;
	flex-direction: column;
}

.single-content .post-autor {
		order: 1;
		display: flex;
		justify-self: start;
		gap: 10px;
		font-size: var(--h8);
		line-height: var(--h8-lh);
		letter-spacing: var(--h8-ls);
		padding: 4px 56px 4px 4px;
		margin-bottom: var(--gapXS);
		border-radius: 100px;
		position: relative;
		z-index: 1;
		background-color: var(--medium-grey);
	}

:is(.single-content .post-autor) p {
			margin: 0;
		}

:is(.single-content .post-autor) a {
			text-decoration: none;
		}

:is(:is(.single-content .post-autor) a):hover {
				text-decoration: none;
			}

:is(.single-content .post-autor):has(a[href=""]) {
			background-color: transparent;
		}

:is(.single-content .post-autor) a::before {
			content: "";
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
		}

:is(.single-content .post-autor):hover {
			text-decoration: none;
		}

:is(.single-content .post-autor) img {
			border-radius: 80px;
			width: 64px;
			height: 64px;
			-o-object-fit: cover;
			   object-fit: cover;
			align-self: center;
		}

:is(.single-content .post-autor) .post-autor-info {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: start;
			gap: 5px;
		}

:is(.single-content .post-autor) .post-autor-nom a {
			display: none;
		}

:is(.single-content .post-autor) .post-autor-nom a[href*="http"] {
			display: block;
		}

:is(.single-content .post-autor) .post-autor-carrec {
			font-size: var(--txt-sm);
			line-height: var(--txt-sm-lh);
			letter-spacing: var(--txt-sm-ls);
			border: var(--border-thin);
			border-radius: 50px;
			padding: 2px 10px;
		}

.single-content .post-aside {
		order: 2;
		display: contents;
	}

:is(.single-content .post-aside) h2 {
			font-size: var(--h8);
			line-height: var(--h8-lh);
			letter-spacing: var(--h8-ls);
			padding: var(--gapXS) 0 var(--gapXXS) 0;
			margin-bottom: 20px;
			border-bottom: 1px solid var(--Brand-Black-50);
		}

.single-content .post-toc {
		order: 2;
		position: sticky;
		top: 20px;
		z-index: 98;
		margin-top: 0;
		background-color: var(--medium-grey);
		border-radius: var(--radiusL);
		padding: 14px 20px;
		transition: border-radius 0.3s;
	}

:is(.single-content .post-toc) p.post-toc-desplegable {
			background: url("../img/ico_arrows.svg") no-repeat right -20px;
			background-size: 10px;
			margin: 0;
			padding: 0;
			transition: margin 0.3s, color 0.3s;
		}

.desplegat:is(.single-content .post-toc) {
			border-radius: var(--radiusL);
		}

.desplegat:is(.single-content .post-toc) p.post-toc-desplegable {
				background-position: right -76px;
				color: var(--Brand-Black-50);
				padding: 0px 0 var(--gapS) 0;
				transition: margin 0.3s, color 0.3s;
			}

:is(.single-content .post-toc) ol {
			counter-reset: item;
			list-style-type: none;
			font-size: var(--h8);
			line-height: var(--h8-lh);
			letter-spacing: var(--h8-ls);
		}

:is(:is(.single-content .post-toc) ol) li {
				counter-increment: item;
				position: relative;
				display: grid;
				grid-template-columns: 41px 1fr;
				gap: var(--gapXS);
				align-items: baseline;
				margin-bottom: 18px;
			}

:is(:is(:is(.single-content .post-toc) ol) li)::before {
					content: counter(item, decimal);
					display: flex;
					width: 41px;
					height: 41px;
					align-items: center;
					justify-content: center;
					background-color: var(--Brand-Black);
					color: var(--bgpage);
					border-radius: 50px;
				}

:is(:is(:is(.single-content .post-toc) ol) li) strong {
					font-weight: 400;
				}

.single-content .recursos {
		order: 10;
		background-color: var(--medium-grey);
		padding: 20px;
		border-radius: var(--radiusL);
		margin-top: var(--gapM);
	}

:is(.single-content .recursos):empty {
			background-color: red;
			padding: 0;
			margin: 0;
		}

:is(.single-content .recursos) h2 {
			display: none;
		}

:is(.single-content .recursos) ul.recursos-list {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
			gap: var(--gapXXS);
		}

:is(:is(.single-content .recursos) ul.recursos-list) li {
				list-style: none;
				max-width: 400px;
			}

.single-content .post-content {
		order: 3;
		margin-top: var(--gapXXL);
	}

:is(.single-content .post-content)  > :first-child {
			margin-top: 0;
		}

:is(.single-content .post-content) h2 {
			font-size: var(--h5);
			line-height: var(--h5-lh);
			letter-spacing: var(--h5-ls);
			margin-top: 40px;
			margin-bottom: 20px;
			scroll-margin-top: 90px;
		}

:is(.single-content .post-content) h3 {
			font-size: var(--txt-xl);
			line-height: var(--txt-xl-lh);
			letter-spacing: var(--txt-xl-ls);
			margin-top: 1em;
			margin-bottom: 0.5em;
		}

:is(.single-content .post-content) h4 {
			font-size: var(--txt-lg);
			line-height: var(--txt-lg-lh);
			letter-spacing: var(--txt-lg-ls);
			margin-top: 1em;
			margin-bottom: 0.5em;
		}

:is(.single-content .post-content) ol,:is(.single-content .post-content) ul {
			margin: 1.5em 0 1.5em 1.25em;
		}

:is(.single-content .post-content) li {
			margin: 0.62em 0;
			padding-left: 0.25em;
		}

:is(.single-content .post-content) li::marker {
			font-size: 0.75em;
		}

:is(.single-content .post-content) .wp-block-table {
			overflow-x: auto;
			width: 100%;
			display: block;
		}

:is(:is(.single-content .post-content) .wp-block-table) table {
				border-collapse: collapse;
				border-spacing: 0;
				width: auto;
				table-layout: fixed;
				max-width: -moz-fit-content;
				max-width: fit-content;
				word-break: normal;
			}

:is(:is(.single-content .post-content) .wp-block-table) th,:is(:is(.single-content .post-content) .wp-block-table) td {
				min-width: 200px;
				border: none;
				border-top: 1px solid var(--Brand-Black);
				font-size: var(--txt-sm);
				line-height: var(--txt-sm-lh);
				letter-spacing: var(--txt-sm-ls);
				padding: 10px 10px 32px 10px;
				vertical-align: top;
				word-break: normal;
			}

:is(:is(.single-content .post-content) .wp-block-table) th td:first-child,:is(:is(.single-content .post-content) .wp-block-table) tr td:first-child {
				padding-left: 0;
			}

:is(:is(.single-content .post-content) .wp-block-table) th td:last-child,:is(:is(.single-content .post-content) .wp-block-table) tr td:last-child {
				padding-right: 0;
			}

:is(.single-content .post-content) hr.wp-block-separator {
			border-top: 1px solid var(--Brand-Black-50);
			margin-bottom: var(--gapXS);
		}

:is(.single-content .post-content) ol.wp-block-footnotes {
			border-top: 1px solid var(--Brand-Black-50);
			font-size: var(--txt-xs);
			line-height: var(--txt-xs-lh);
			letter-spacing: var(--txt-xs-ls);
			padding: 10px 0 0;
			margin: var(--gapXXL) 0 0 0;
		}

:is(:is(.single-content .post-content) ol.wp-block-footnotes) li {
				scroll-padding-top: var(--gapXL);
				list-style-type: none;
				counter-increment: footnote;
				position: relative;
				padding-left: 20px;
				margin: 0 0 var(--gapXS) 0;
			}

:is(:is(.single-content .post-content) ol.wp-block-footnotes) li::before {
				content: counter(footnote);
				position: absolute;
				left: 0;
				font-weight: 700;
			}

/**************************
PPP - SINGLE EQUIP
***************************/

.single-equip {
	display: grid;
	grid-template-columns: 1fr;
	padding-bottom: 80px;
}

.single-equip .single-equip-media {
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		margin-bottom: var(--gapM);
		max-width: 440px;
	}

:is(.single-equip .single-equip-media) img {
			grid-column: 1/-2;
			border-radius: var(--radiusM);
			margin-bottom: var(--gapXS);
		}

.single-equip .single-equip-info {
		display: flex;
		flex-direction: column;
	}

.single-equip .single-equip-titol {
		margin-bottom: var(--gapXS);
		order: 1;
	}

.single-equip .single-equip-meta {
		margin-bottom: var(--gapXL);
		order: 2;
	}

.single-equip blockquote.single-equip-cita {
		font-size: var(--quote-ficha);
		line-height: var(--quote-ficha-lh);
		letter-spacing: var(--quote-ficha-ls);
		margin: 0;
		padding-left: 30px;
		margin-bottom: var(--gapXL);
		background: url("../img/ico_cita_xl.svg") no-repeat left 5px;
		background-size: 17px auto;
		quotes: " " "\00201D""\002018""\002019";
		order: 3;
	}

.single-equip .single-equip-contingut {
		-moz-column-width: 460px;
		     column-width: 460px;
		-moz-column-gap: 40px;
		     column-gap: 40px;
		widows: 3;
		orphans: 3;
		order: 4;
	}

:is(.single-equip .single-equip-contingut) p {
			-moz-column-break-inside: avoid;
			     break-inside: avoid;
		}

:is(.single-equip .single-equip-contingut) :last-child {
			margin-bottom: 0;
		}

.equip-perfil,
.single-equip-meta {
	margin: 0;
	display: flex;
	flex-wrap: wrap;
	gap: 4px;
}

:is(.equip-perfil,.single-equip-meta) li,:is(.equip-perfil,.single-equip-meta) p.email {
		border: var(--border-thin);
		border-radius: 1000px;
		padding: 3px 8px;
		font-size: var(--txt-xs);
		line-height: 1;
		letter-spacing: var(--txt-xs-ls);
		list-style-type: none;
		margin: 0;
	}

:is(:is(.equip-perfil,.single-equip-meta) li,:is(.equip-perfil,.single-equip-meta) p.email) a {
			display: flex;
			align-items: center;
			gap: 8px;
		}

:is(:is(:is(.equip-perfil,.single-equip-meta) li,:is(.equip-perfil,.single-equip-meta) p.email) a)::after {
				content: "";
				background: url("../img/ico_arrow_diagonal.svg") no-repeat center;
				width: 10px;
				height: 10px;
				background-size: contain;
				transform: rotate(0);
				transition: all 0.3s;
			}

:is(:is(:is(.equip-perfil,.single-equip-meta) li,:is(.equip-perfil,.single-equip-meta) p.email) a):hover::after {
				margin-left: 10px;
				transform: rotate(45deg);
				transition: all 0.3s;
			}

:is(:is(:is(.equip-perfil,.single-equip-meta) li,:is(.equip-perfil,.single-equip-meta) p.email) a):hover {
				text-decoration: none;
			}

.single-equip-meta {
	gap: 8px;
}

.single-equip-meta li,.single-equip-meta p.email {
		padding: 7px 12px 8px 12px;
		font-size: var(--txt-md);
		line-height: var(--txt-md-lh);
		letter-spacing: var(--txt-md-ls);
	}

ul.equip-xxss {
	grid-column: 1/-1;
	display: flex;
	gap: 10px;
	list-style-type: none;
	margin-left: 0;
}

ul.equip-xxss a {
		border-radius: 50px;
		width: 32px;
		height: 32px;
		border: 1.3px solid var(--Brand-Black, #3c3f36);
		display: block;
		text-indent: -999em;
		background-repeat: no-repeat;
		background-position: center;
		background-color: var(--Brand-Black, #3c3f36);
		transition: all 0.3s;
	}

:is(ul.equip-xxss a):hover {
			background-color: transparent;
			filter: brightness(0.25);
			transition: all 0.3s;
		}

ul.equip-xxss a[href*="x.com"] {
		background-image: url("../img/ico_twitter.svg");
		background-size: 13px auto;
	}

ul.equip-xxss a[href*="linkedin"] {
		background-image: url("../img/ico_linkedin.svg");
		background-size: 16px auto;
	}

.single-equipo .bloc-relacionats {
	background-color: var(--medium-grey);
}

/**************************
PPP - LLISTAT ÀREES - SERVEIS
***************************/

.page-servicios {
	background: url("../img/textura-servicios.jpg") no-repeat left top;
	background-size: cover;
	margin-top: -81px;
	padding-top: calc(200px + 40px);
}

.page-servicios .pagina-intro {
		font-size: var(--txt-lg);
		line-height: var(--txt-lg-lh);
		letter-spacing: var(--txt-lg-ls);
	}

:is(.page-servicios .pagina-intro) h2 {
			font-size: var(--nombre-ficha);
			line-height: var(--nombre-ficha-lh);
			padding-top: var(--gapXXS);
			border-top: 1px solid var(--Brand-Black-50);
			margin-bottom: 10px;
			margin-top: var(--gapL);
			padding-right: var(--gapXL);
			position: relative;
			transition: margin-bottom 0.3s;
		}

:is(:is(.page-servicios .pagina-intro) h2)::after {
				content: "";
				margin-left: 8px;
				width: 20px;
				height: 20px;
				background: url("../img/ico_desplegable.svg") no-repeat center top;
				background-size: 100% auto;
				position: absolute;
				top: 10px;
				right: 2px;
			}

.desplegat:is(:is(.page-servicios .pagina-intro) h2)::after {
					background-position: center -91px;
				}

:is(.page-servicios .pagina-intro) .intro-pagina-txt > .wp-block-group__inner-container > p:first-of-type {
			margin-bottom: 40px;
		}

/**************************
PPP - SINGLE ÀREA / SERVICIO
***************************/

.single-categoria_terraqui_a {
	background-color: #fff;
}

.area-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100dvh;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	z-index: -1;
}

.single-categoria_terraqui_a main {
	padding-top: 0;
}

.single-area-header {
	height: calc(100dvh - 80px);
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 160px 0;
	position: relative;
}

.single-area-header h1 {
		margin-bottom: 1.5em;
		font-size: var(--h1);
		line-height: var(--h1-lh);
		letter-spacing: var(--h1-ls);
		text-align: center;
		text-wrap: balance;
	}

.single-area-header .anchor_down a {
		display: block;
		width: 40px;
		height: 40px;
		background: url("../img/ico_arrow_down_anchor.svg") no-repeat center;
		background-size: contain;
		text-indent: -999em;
		position: absolute;
		bottom: 32px;
		left: 50%;
		transform: translateX(-50%);
	}

:is(section[class*="area-bloc"] .wrap > .wp-block-group__inner-container) h2 {
			font-size: var(--h4);
			line-height: 1.2;
			-moz-column-span: all;
			     column-span: all;
			margin-bottom: var(--gapM);
			max-width: 900px;
			text-wrap: balance;
		}

:is(section[class*="area-bloc"] .wrap > .wp-block-group__inner-container) ul {
			padding-left: 1.25em;
		}

:is(section[class*="area-bloc"] .wrap > .wp-block-group__inner-container) li {
			-moz-column-break-inside: avoid;
			     break-inside: avoid;
			padding-left: 0.25em;
			margin-bottom: 0.5em;
		}

.area-bloc1 {
	padding: 40px 0 var(--gapXL) 0;
	font-size: var(--txt-md);
	line-height: var(--txt-md-lh);
	letter-spacing: var(--txt-md-ls);
}

.area-bloc2 {
	padding: var(--gapXXL) 0 80px 0;
	background-color: var(--bgpage);
}

.single-categoria_terraqui_a .servicios-lista {
	background-color: var(--bgpage);
}

a.ver-mas-link {
	font-size: var(--txt-md);
	line-height: var(--txt-md-lh);
	letter-spacing: var(--txt-md-ls);
	color: var(--Brand-Black-50);
	display: flex;
	align-items: center;
	gap: 10px;
	margin-top: 20px;
}

a.ver-mas-link::after {
		content: "";
		width: 16px;
		height: 12px;
		background: url("../img/ico_arrows.svg") no-repeat left -34px;
		background-size: 13px auto;
		opacity: 50%;
	}

/***********************
Pàgina bàsica amb títol
	***********************/
.page-template-page-with-title h1 {
		font-size: var(--h2);
		line-height: var(--h2-lh);
		letter-spacing: var(--h2-ls);
		margin-bottom: var(--gapL);
	}
.page-template-page-with-title .single-content {
		padding-bottom: var(--gap-page-bottom);
	}

/***********************
	Contacte
	***********************/

.contacte-info {
	max-width: 890px;
	margin-bottom: 56px;
	font-size: var(--txt-lg);
	line-height: var(--txt-lg-lh);
	letter-spacing: var(--txt-lg-ls);
}

.contacte-info h2 {
		font-size: var(--h2);
		line-height: var(--h2-lh);
		letter-spacing: var(--h2-ls);
		margin-bottom: 20px;
	}

.contacte-info p {
		margin-bottom: 20px;
	}

.contacte-info strong {
		font-weight: 400;
		min-width: 62px;
		display: inline-block;
	}

.contacte-form {
	color: #fff;
	background-color: var(--Brand-Black);
	padding: var(--gapL) var(--gapM);
	border-radius: var(--radiusL) 0;
}

.contacte-form form.wpcf7-form {
		display: grid;
		grid-template-columns: 1fr;
		gap: 40px;
	}

.contacte-form p {
		margin-bottom: 0;
	}

.contacte-form h3 {
		font-size: var(--h5);
		margin-bottom: var(--gapXL);
	}

.contacte-form input[type="text"],.contacte-form input[type="email"],.contacte-form textarea {
		background-color: var(--Brand-Black);
		color: #fff;
		border-bottom: 1px solid #fff;
	}

:is(.contacte-form input[type="text"],.contacte-form input[type="email"],.contacte-form textarea)::-moz-placeholder {
			color: rgba(255, 255, 255, 0.5);
		}

:is(.contacte-form input[type="text"],.contacte-form input[type="email"],.contacte-form textarea)::placeholder {
			color: rgba(255, 255, 255, 0.5);
		}

.contacte-form textarea {
		max-height: calc(1.35em * 3 + var(--gapS));
	}

.contacte-form input[type="submit"] {
		font-size: var(--txt-md);
		line-height: var(--txt-md-lh);
		letter-spacing: var(--txt-md-ls);
		margin-top: var(--gapXXS);
		background-color: var(--Brand-Black);
		color: var(--bgpage);
		border-color: var(--bgpage);
		padding-right: 37px;
		background: url("../img/ico_arrow_submit_contact.svg") no-repeat calc(100% - 14px) 11px;
		background-size: 14px auto;
	}

/** FOOTER **/

footer#colophon {
	background-color: var(--Brand-Black);
	padding: 40px 0 var(--gapM) 0;
	color: var(--yellow);
}

footer#colophon p {
		margin: 0;
	}

footer#colophon a,footer#colophon a:visited {
		color: var(--yellow);
		text-decoration: underline;
		text-underline-offset: 0.3em;
	}

.site-branding-footer {
	margin-bottom: 9px;
}

.site-branding-footer .logo-footer-desktop {
		display: none;
	}

.footer-top {
	margin-bottom: var(--gapS);
}

.footer-info-wrapper {
	display: flex;
	flex-direction: column;
	gap: 22px;
	padding-top: var(--gapXL);
	line-height: 2;
}

.footer-info-wrapper strong {
		font-weight: 400;
		width: 52px;
		display: inline-block;
	}

.footer-info-wrapper .widget:nth-of-type(2) {
		order: 5;
		line-height: 135%;
		margin-top: 6px;
	}

.footer-bottom {
	display: flex;
	flex-direction: column;
	gap: var(--gapS);
	padding-top: var(--gap-page-top);
	border-top: 1px solid var(--white-20);
	font-size: var(--txt-xs);
}

.footer-bottom .footer-estudi {
		text-transform: uppercase;
	}

.footer-bottom .menu-footer-container {
		order: -1;
	}

.footer-bottom ul[id*="menu-footer"] {
		display: flex;
		list-style-type: none;
		gap: 14px;
	}

/* Newsletter */

.newsletter {
	background-color: #fff;
}

.newsletter .wrap {
		padding: 40px 0 var(--gapL) 0;
	}

:is(.newsletter .wrap) .newsletter-txt h2 {
			font-size: var(--h3-nws);
			line-height: var(--h3-nws-lh);
			letter-spacing: var(--h3-nws-ls);
			margin-bottom: var(--gapM);
			text-wrap: balance;
		}

:is(.newsletter .wrap) .boto-newsletter {
			justify-self: end;
			display: inline-flex;
			margin: 0;
		}

.home .newsletter,
.page-servicios + .servicios-lista + .newsletter {
	background-color: var(--bgpage);
}

.pagination {
	grid-column: 1/-1;
	display: flex;
	justify-content: center;
}

.pagination .pagination-inner {
		margin: var(--gapXL) auto 0 auto;
		display: flex;
		justify-content: center;
		gap: 4px;
		background-color: var(--medium-grey);
		padding: 4px;
		border-radius: 100px;
	}

:is(.pagination .pagination-inner) .page-numbers {
			width: 48px;
			height: 48px;
			border-radius: 56px;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: var(--txt-lg);
			line-height: 1;
			color: var(--Brand-Black);
			transition: all 0.3s;
		}

.current:is(:is(.pagination .pagination-inner) .page-numbers),:is(:is(.pagination .pagination-inner) .page-numbers):hover {
				text-decoration: none;
				background-color: var(--Brand-Black);
				color: var(--bgpage);
				transition: all 0.3s;
			}

.next:is(:is(.pagination .pagination-inner) .page-numbers),.prev:is(:is(.pagination .pagination-inner) .page-numbers) {
				width: 67px;
				text-indent: -999em;
				background-position: center 14px;
				background-repeat: no-repeat;
				text-indent: -9999em;
				background-color: var(--Brand-Black);
				background-size: 26px auto;
				transition: background-color 0.3s;
			}

:is(.next:is(:is(.pagination .pagination-inner) .page-numbers),.prev:is(:is(.pagination .pagination-inner) .page-numbers)):hover {
					background-position: center -105px;
					background-color: var(--bgpage);
					transition: background-color 0.3s;
				}

.dots:is(:is(.pagination .pagination-inner) .page-numbers):hover {
				background-color: transparent;
				color: var(--Brand-Black);
			}

.next:is(:is(.pagination .pagination-inner) .page-numbers) {
				background-image: url("../img/ico_arrow_pag_next.svg");
				margin-left: 12px;
			}

.prev:is(:is(.pagination .pagination-inner) .page-numbers) {
				background-image: url("../img/ico_arrow_pag_prev.svg");
				margin-right: 12px;
			}

/***********************
	PATRONS 
	***********************/

/* Intro página */

.pagina-intro {
	padding-bottom: var(--gapL);
}

.pagina-intro h2.intro-pagina-titol,.pagina-intro p.intro-pagina-titol {
		font-size: var(--h2);
		letter-spacing: var(--h2-ls);
		line-height: var(--h2-lh);
		text-wrap: balance;
		margin-bottom: var(--gapS);
	}

.pagina-intro .intro-pagina-txt p:last-child {
		margin-bottom: 0;
	}

@media (min-width: 600px) {
	/** Carousel home **/
		.carousel-home-container .carousel-item .carousel-image {
			aspect-ratio: unset;
		}

			:is(.carousel-home-container .carousel-item .carousel-image) img {
				height: 30vh;
			}

		.carousel-home-container .slick-arrow {
			top: 25vh;
		}
	/** Teaser grid **/

	.grid-teasers {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-column-gap: var(--gapXS);
		grid-row-gap: 56px;
	}

	.home .grid-teasers {
		display: block;
	}
		.grid-publicaciones .grid-teasers {
			grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
		}

	/* Teaser notícies */

	.teaser-tipus a {
		padding: 4px 12px;
	}

	/* Filtres archive */

	.archive-filtres {
		max-width: none;
	}

	.archive-filtres-grup {
		flex-wrap: nowrap;
	}

		.archive-filtres-grup .filtre-tipus {
			width: auto;
		}

		.archive-filtres-grup .js-filtre-desplegable {
			max-width: none;
		}
		body[class*="page-publicaciones"] .archive-filtres {
			display: grid;
			grid-template-columns: 1fr 1fr;
		}
		body[class*="page-publicaciones"] .filtre-area-header {
			display: inline-flex;
		}
}

@media (min-width: 768px) {
	/** Header **/

	#masthead .wrap {
		display: grid;
		grid-template-columns: 280px 1fr max-content;
		align-items: center;
		padding: 23px 0;
		gap: 36px;
	}

	#site-navigation {
		justify-self: end;
	}

	.site-branding {
		max-width: 100%;
		margin-left: 0;
	}

	/* Botons */

	.boto a,
	.contacte-info a[href*="maps"],
	button.apply-filters-btn,
	.klaro .cn-button,
	#klaro-modal .cm-button,
	.recursos .boto a {
		font-size: var(--txt-md);
		line-height: var(--txt-md-lh);
		letter-spacing: var(--txt-md-ls);
		padding: 8px 20px;
	}

	.carousel-home .carousel-text a,
	.boto-newsletter a {
		font-size: var(--h5);
		line-height: var(--h5-lh);
		letter-spacing: var(--h5-ls);
		padding: 13px 24px;
	}

	.recursos .boto a,
	.boto.ver-mas a,
	.contacte-info a[href*="maps"],
	.carousel-home .carousel-text a {
		padding-right: 15px;
	}
		:is(.recursos .boto a,.boto.ver-mas a,.contacte-info a[href*="maps"],.carousel-home .carousel-text a)::after {
			background: url("../img/ico_arrow_down.svg") no-repeat center;
			background-size: contain;
			width: 22px;
			height: 22px;
		}
		:is(.boto.ver-mas a)::after {
			transform: rotate(-135deg);
			transition: all 0.3s;
		}

		:is(.boto.ver-mas a):hover::after {
			margin-left: 10px;
			transform: rotate(-90deg);
			transition: all 0.3s;
		}

	.carousel-home .carousel-text a::after,
	.boto-newsletter a::after {
		width: 30px;
		height: 30px;
	}

	/* Home */
		.carousel-home-container .carousel-content {
			font-size: var(--h3);
			line-height: var(--h3-lh);
			letter-spacing: var(--h3-ls);
			padding: 5vw;
		}

	.home .grid-teasers {
		display: grid;
	}

	.home .bloc-relacionats {
		padding-bottom: var(--gap-page-bottom);
	}

	/* Bloc relacionats */

	.bloc-relacionats-header {
		align-items: start;
	}

		.bloc-relacionats-header .boto.ver-mas a {
			text-indent: 0;
			background-color: var(--Brand-Black);
			border: var(--border);
		}

			:is(.bloc-relacionats-header .boto.ver-mas a):hover {
				background-color: var(--Brand-Black) !important;
			}

			:is(.bloc-relacionats-header .boto.ver-mas a):after {
				filter: brightness(1000%);
			}

	/* Llistat àrees - serveis */

	.servicios-lista ol a {
		background-position: calc(100% - 4px) 9px;
		background-size: 20px auto;
	}

	/** Post content **/
			:is(.single-content .post-content) h2 {
				margin-top: 56px;
				margin-bottom: 0.7em;
				scroll-margin-top: 40px;
			}

			:is(.single-content .post-content) h3,:is(.single-content .post-content) h4,:is(.single-content .post-content) h5,:is(.single-content .post-content) h6 {
				font-weight: 700;
				margin-top: 2em;
			}

			:is(.single-content .post-content) p,:is(.single-content .post-content) ul,:is(.single-content .post-content) ol,:is(.single-content .post-content) blockquote {
				margin-bottom: 1.3em;
			}

			:is(.single-content .post-content) img {
				margin: 28px 0;
			}

			:is(.single-content .post-content) ul,:is(.single-content .post-content) ol {
				margin-left: 1.25em;
			}
				:is(:is(.single-content .post-content) figure.wp-block-table) td,:is(:is(.single-content .post-content) figure.wp-block-table) th {
					padding-bottom: 40px;
					min-width: 120px;
					width: auto;
				}

			:is(.single-content .post-content) a[href*="#"] {
				position: relative;
				top: -0.1em;
				font-weight: 700;
				font-size: 0.875rem;
			}

			:is(.single-content .post-content) ol.wp-block-footnotes {
				-moz-column-width: 400px;
				     column-width: 400px;
				-moz-column-gap: var(--gapS);
				     column-gap: var(--gapS);
				widows: 3;
				orphans: 3;
				order: 4;
			}

				:is(:is(.single-content .post-content) ol.wp-block-footnotes) li {
					margin-bottom: var(--gapS);
					padding-left: var(--gapS);
				}

	/* Teaser notícies */

	.teaser-media {
		margin-bottom: var(--gapXS);
	}

	/* Teaser publicacions */

	.teaser-article.publicacion {
		min-height: 460px;
	}

		.teaser-article.publicacion .publicacion-download a {
			width: 41px;
			height: 41px;
		}
		body[class*="page-publicaciones"] .grid-teasers {
			gap: 40px;
		}

	/* Teaser equip */

	.teaser-equip {
		display: grid;
		grid-template-columns: 1fr;
		grid-template-rows: max-content 1fr;
		row-gap: 20px;
		position: relative;
	}

		.teaser-equip img {
			grid-column: 1/2;
			grid-row: 1/2;
			margin-bottom: 0;
		}

		.teaser-equip .teaser-equip-info {
			grid-column: 1/2;
			grid-row: 2/3;
			z-index: 3;
			padding: 0 var(--gapM) var(--gapM) var(--gapM);
			gap: 18px;
		}

		.teaser-equip .teaser-equip-over {
			display: block;
			grid-column: 1/2;
			grid-row: 1/3;
			background-color: var(--dark-grey);
			border-radius: var(--radiusM);
			transition: all 0.3s;
			padding: var(--gapM);
			opacity: 0;
			height: 0;
		}

			:is(.teaser-equip .teaser-equip-over) blockquote {
				margin: 0;
			}

			:is(.teaser-equip .teaser-equip-over)::before {
				display: block;
				content: url("../img/ico_veure_mes_black_L.svg");
				margin-bottom: 22px;
			}

		.teaser-equip:hover .teaser-equip-over {
			height: 100%;
			opacity: 1;
			transition: all 0.3s;
		}

	/* Single article */

	.single-content .post-toc p.post-toc-desplegable {
		background-position: right -34px;
		background-size: 16px auto;
	}

	/** FOOTER **/

	footer#colophon {
		padding: 80px 0 40px 0;
	}

		footer#colophon a,footer#colophon a:visited {
			text-decoration: none;
		}
		.site-branding-footer .logo-footer-mobil {
			display: none;
		}

		.site-branding-footer .logo-footer-desktop {
			display: block;
		}

	.site-branding-footer {

		margin: 0;
	}

	.footer-top {
		margin-bottom: var(--gap-page-top);
	}

		.footer-top .logo-footer-desktop {
			max-width: 50%;
			margin-bottom: var(--gapXL);
		}

	.footer-info-wrapper {
		flex-direction: row;

		gap: var(--gapL);
		padding-top: 0;
		align-items: start;
		line-height: 135%;
	}

		.footer-info-wrapper strong {
			width: 62px;
		}

		.footer-info-wrapper .widget.widget_block {
			order: initial;
		}

		.footer-info-wrapper .widget:nth-of-type(2) {
			margin-top: 0;
		}

		.footer-info-wrapper .widget:last-of-type strong {
			display: none;
			margin-top: 0;
		}

	.footer-bottom {
		flex-direction: row;
		justify-content: space-between;
		gap: var(--gapXS);
		padding-top: 12px;
	}

		.footer-bottom .menu-footer-container {
			order: initial;
			justify-self: end;
		}

		.footer-bottom ul[id*="menu-footer"] {
			gap: 0;
		}
				:is(:is(.footer-bottom ul[id*="menu-footer"]) a)::after {
					content: "|";
					padding-left: var(--gapS);
					padding-right: var(--gapS);
					color: var(--yellow);
				}
				:is(:is(.footer-bottom ul[id*="menu-footer"]) li:last-of-type a)::after {
					content: "";
					padding: 0;
				}

	/* FORMULARIS */

	input[type="text"],
	input[type="email"],
	textarea {
		padding: 0 0 20px 0;
	}

	/** NEWSLETTER */
		.newsletter .wrap {
			display: grid;
			grid-template-columns: minmax(300px, 980px) 1fr;
			gap: var(--gapXS);
			align-items: start;
			padding: var(--gapXXL) 0 96px 0;
		}

			:is(.newsletter .wrap) .newsletter-txt {
				margin-bottom: 0;
				padding-right: var(--gapXL);
			}

			:is(.newsletter .wrap) input[type="email"] {
				padding-right: 40px;
				padding-top: 4px;
				padding-bottom: 20px;
				font-size: var(--txt-md);
				letter-spacing: var(--txt-md-ls);
			}

				:is(:is(.newsletter .wrap) input[type="email"])::-moz-placeholder {
					color: var(--Brand-Black-50);
				}

				:is(:is(.newsletter .wrap) input[type="email"])::placeholder {
					color: var(--Brand-Black-50);
				}

			:is(.newsletter .wrap) input[type="submit"] {
				width: 20px;
				height: 20px;
				margin-top: 0;
			}

			:is(.newsletter .wrap) .mailchimp-explain {
				padding-top: 8px;
			}

	/** Paginació **/
		.pagination .pagination-inner {
			margin: var(--gap-page-top) auto 0 auto;
		}
			:is(.pagination .pagination-inner) .page-numbers {
				width: 56px;
				height: 56px;
				font-size: var(--h7);
			}
				.next:is(:is(.pagination .pagination-inner) .page-numbers),.prev:is(:is(.pagination .pagination-inner) .page-numbers) {
					width: 96px;
					background-position: center 11px;
					background-size: 44px auto;
				}
					:is(.next:is(:is(.pagination .pagination-inner) .page-numbers),.prev:is(:is(.pagination .pagination-inner) .page-numbers)):hover {
						background-position: center -189px;
					}

				.next:is(:is(.pagination .pagination-inner) .page-numbers) {
					margin-left: 32px;
				}

				.prev:is(:is(.pagination .pagination-inner) .page-numbers) {
					margin-right: 32px;
				}
}

@media (min-width: 1000px) {
	/** HOME **/

	.carousel-home .carousel-text {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
		:is(.carousel-home .carousel-text,.boto-newsletter) a {
			padding: 13px 24px 13px 26px;
		}
			:is(:is(.carousel-home .carousel-text,.boto-newsletter) a)::after {
				margin-left: 4px;
				transform: rotate(-135deg);
				transition: transform 0.3s, margin-left 0.3s;
				width: 30px;
				height: 30px;
			}
			:is(:is(.carousel-home .carousel-text,.boto-newsletter) a):hover::after {
				margin-left: 8px;
				transform: rotate(-90deg);
				filter: brightness(1000%);
				transition: transform 0.3s, margin-left 0.3s;
			}

	/** PPP - ARTICLES I NOTÍCIES **/

	.archive-filtres {
		display: grid;
		grid-template-columns: 3fr 1fr !important;
		gap: var(--gapXS);
		margin-bottom: var(--gapXXL);
		max-width: none;
	}

	.archive-filtres-grup {
		grid-column: 1/2;
		grid-row: 1/2;
		gap: var(--gapXS);
	}

	.filtre-tipus {
		width: auto;
		padding: 4px;
	}
			:is(.filtre-tipus li) a,:is(.filtre-tipus li) a:visited {
				padding: 10px 24px;
				font-size: var(--txt-sm);
				line-height: var(--txt-sm-lh);
			}

	.filtre-area-header {
		display: inline-flex;
	}
		.filtre-area-header:hover {
			background-color: var(--Brand-Black);
			color: var(--medium-grey);
			transition: all 0.3s;
		}
			.filtre-area-header:hover .filtre-area-toggle {
				background-position: left 3px;
			}

	.filtre-area-toggle {
		width: 14px;
		height: 14px;
		background: url(../img/ico_arrows.svg) no-repeat left -35px;
		background-size: 14px auto;
	}

	.filtre-area-item input[type="checkbox"] {
		width: 11px;
		height: 11px;
	}

	.filtres-aplicats li {
		border: var(--border);
		padding: 11px 16px 12px 16px;
	}
		.archive-cerca button[type="submit"] {
			background-size: 22px auto;
			right: 24px;
		}

	/*** Contacte ***/
		main:has(.contacte-form) .entry-content {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: var(--gapXXS);
		}

	.contacte-info {
		padding-top: 20px;
		font-size: var(--txt-md);
		line-height: var(--txt-md-lh);
		letter-spacing: var(--txt-md-ls);
	}

		.contacte-info h2,.contacte-info p {
			margin-bottom: var(--gapL);
		}

		.contacte-info a[href*="maps"] {
			padding: 10px 21px;
		}
			:is(.contacte-info a[href*="maps"])::after {
				margin-left: 2px;
				transform: rotate(-135deg);
				transition: transform 0.3s, margin-left 0.3s;
			}
			:is(.contacte-info a[href*="maps"]):hover::after {
				margin-left: 8px;
				transform: rotate(-90deg);
				filter: brightness(1000%);
				transition: transform 0.3s, margin-left 0.3s;
			}

	.contacte-form {
		max-width: 800px;
		margin-right: var(--gapXS);
		padding: 72px 88px var(--gapL) 88px;
	}

		.contacte-form form.wpcf7-form {
			grid-template-columns: 1fr 1fr;
			gap: var(--gapM);
		}

		.contacte-form p:has(textarea) {
			grid-column: 1/-1;
		}

		.contacte-form textarea {
			max-height: calc(1.35em * 3);
		}

		.contacte-form input[type="submit"] {
			background: url("../img/ico_arrow_submit_contact.svg") no-repeat calc(100% - 22px) 16px;
			padding: 12px 60px 12px 22px;
			margin-top: 38px;
			transition: padding-right 0.3s;
		}
			:is(.contacte-form input[type="submit"]):hover {
				padding-right: 68px;
				transition: padding-right 0.3s;
			}
}
@media (min-width: 1200px) {
	.grid-teasers.grid-equip {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media (min-width: 1280px) {
	.wrap10 {
		max-width: 1490px;
	}

	/* Header */

	.site-branding {
		position: relative;
		left: calc(var(--gapXS) * -1);
	}

	#masthead.sticky {
		position: sticky;
		top: 0;
		background-color: rgba(255, 255, 255, 0.9);
		box-shadow: 0 8px 12px hsla(0, 0%, 0%, 0.05);
		z-index: 99;
		transition: all 0.3s;
	}

	/*** PPP - Home ***/

	.top-home {
		margin-top: -104px;
		padding-top: calc(200px + 104px);
		padding-bottom: var(--gapXXL);
	}
		.carousel-home-container .carousel-item {
			display: grid;
			grid-template-columns: 1fr 1fr;
		}

			:is(.carousel-home-container .carousel-item) .carousel-image {
				aspect-ratio: initial;
			}

				:is(:is(.carousel-home-container .carousel-item) .carousel-image) img {
					height: auto;
					max-height: none;
				}
			:is(.carousel-home-container .carousel-item) .carousel-content {
				font-size: var(--h3);
				line-height: var(--h3-lh);
				letter-spacing: var(--h3-ls);
				min-height: 0;
				display: flex;
			}

				:is(:is(.carousel-home-container .carousel-item) .carousel-content) p {
					margin: 0;
				}

				:is(:is(.carousel-home-container .carousel-item) .carousel-content) .carousel-text p:has(a) {
					margin-top: auto;
				}

		.carousel-home-container .slick-arrow {
			width: 41px;
			height: 32px;
			top: 50%;
			transform: translateY(-50%);
			bottom: auto;
		}

			.slick-next:is(.carousel-home-container .slick-arrow) {
				right: 40px;
				background-image: url(../img/ico_arrow_next.svg);
				filter: brightness(0);
			}

			.slick-prev:is(.carousel-home-container .slick-arrow) {
				background-image: url(../img/ico_arrow_prev.svg);
				right: auto;

				left: 40px;
			}

	#home-slogan {
		font-size: var(--h1);
		line-height: var(--h1-lh);
		letter-spacing: var(--h1-ls);
		padding: var(--gapXXL) 0 var(--gap-page-bottom) 0;
	}

		#home-slogan p {
			margin-bottom: 0;
		}

	.home .servicios-lista {
		background-color: #fff;
		padding: 88px 0;
		letter-spacing: 1%;
	}

		:is(.home .servicios-lista) .wrap {
			display: grid;
			grid-template-columns: repeat(12, 1fr);
		}
		:is(.home .servicios-lista) h2 {
			grid-column: 1/5;
			padding-top: 163px;
			padding-bottom: 28px;
			margin-bottom: 0;
			font-size: var(--categorias-home);
			line-height: 1;
		}

		:is(.home .servicios-lista) .categorias-terraqui {
			grid-column: 6/-1;
			position: relative;
		}

			:is(:is(.home .servicios-lista) .categorias-terraqui) ol {
				display: block;
				font-size: var(--categorias-home);
				line-height: 1;
				list-style-type: none;
				height: 440px;
				overflow-y: scroll;
				scroll-snap-type: y mandatory;
				scrollbar-width: none;
				-ms-overflow-style: none;
				overscroll-behavior: contain;
				scroll-behavior: smooth;
			}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol)::-webkit-scrollbar {
					display: none;
				}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) li {
					list-style-type: none;
					scroll-snap-align: center;
					scroll-snap-stop: always;
					padding: 0 0 28px 0;
					z-index: 1;
					border: none;
					text-wrap: balance;
				}

					:is(:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) li)::before {
						content: none;
					}

					:is(:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) li):first-of-type {
						padding-top: 163px;
					}
					:is(:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) li):last-of-type {
						padding-bottom: 213px;
					}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) a {
					padding-right: 0;
					padding-left: 0;
					background: none;
				}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) a:hover {
					text-decoration: none;
					padding-left: 0;
				}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) a::after {
					content: url("../img/ico_arrow_servicios_home.svg");
					padding-left: 21px;
					padding-right: 20px;
					transition: padding 0.3s;
					white-space: nowrap;
				}

				:is(:is(:is(.home .servicios-lista) .categorias-terraqui) ol) a:hover::after {
					padding-left: 40px;
					padding-right: 0;
					transition: padding 0.3s;
				}
			:is(:is(.home .servicios-lista) .categorias-terraqui)::before {
				content: "";
				width: 100%;
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				height: 140px;
				background: linear-gradient(180deg, #fff 7.2%, rgba(255, 255, 255, 0) 100%);
				z-index: 2;
			}

			:is(:is(.home .servicios-lista) .categorias-terraqui)::after {
				content: "";
				width: 100%;
				position: absolute;
				bottom: 0;
				right: 0;
				left: 0;
				height: 140px;
				background: linear-gradient(180deg, rgba(255, 255, 255, 0) 7.2%, rgba(255, 255, 255, 1) 100%);
				z-index: 2;
			}

	/*** PPP - BLOC INTRO PÀGINES ***/

	.pagina-excerpt {
		line-height: var(--h2-lh);
	}

	/*** PPP - TEASERS ***/

	/* Grid teasers */

	.grid-teasers {
		grid-template-columns: repeat(3, 1fr);
	}

	.grid-teasers.grid-equip {
		padding-top: var(--gapXS);
	}
		:is(.bloc-relacionats .grid-teasers .teaser-article):nth-child(n + 5) {
			display: flex;
		}

		:is(.bloc-relacionats .grid-teasers .teaser-article):nth-child(n + 7) {
			display: none;
		}
			:is(:is(.home,.single-post) .bloc-relacionats .grid-teasers .teaser-article):nth-child(n + 4) {
				display: none;
			}
	/* Teaser notícies */

	.no-thumbnail .teaser-media a[rel="bookmark"]::after {
		right: var(--gapS);
		bottom: var(--gapS);
	}

	h2.teaser-titol {
		margin-bottom: 22px;
	}

	/*** BLOC RELACIONATS ***/

	.bloc-relacionats-header {
		margin-bottom: 40px;
	}

	.home .bloc-relacionats {
		padding: var(--gapS) 0 120px 0;
	}

		:is(.home .bloc-relacionats) .tns-nav {
			display: none;
		}

	.single-categoria_terraqui_a .bloc-relacionats .bloc-relacionats-header h2 {
		font-size: var(--h2);
		line-height: var(--h2-lh);
		letter-spacing: var(--h2-ls);
	}

	/*** PPP - SINGLE MEMBRE ***/

	.single-equip {
		grid-template-columns: repeat(12, 1fr);
		gap: var(--gapXS);
		padding-bottom: 200px;
	}

		.single-equip .single-equip-media {
			display: block;
			grid-column: 1/5;
			margin-bottom: var(--gapXS);
			max-width: none;
			padding-right: 68px;
		}

		.single-equip h1.single-equip-titol {
			padding-left: 68px;
			margin-bottom: 20px;
			font-size: var(--h3);
		}

		.single-equip .single-equip-meta {
			gap: 8px;
			padding-left: 68px;
		}
			:is(.single-equip .single-equip-meta) li,:is(.single-equip .single-equip-meta) p.email {
				padding: 10px 20px 11px 20px;
				font-size: var(--txt-md);
				line-height: var(--txt-md-lh);
				letter-spacing: var(--txt-md-ls);
				border: var(--border);
			}

				:is(:is(.single-equip .single-equip-meta) li,:is(.single-equip .single-equip-meta) p.email) a {
					gap: 10px;
				}

					:is(:is(:is(.single-equip .single-equip-meta) li,:is(.single-equip .single-equip-meta) p.email) a)::after {
						width: 14px;
						height: 14px;
					}

		.single-equip .single-equip-info {
			grid-column: 5/-1;
			display: flex;
			flex-direction: column;
		}

		.single-equip blockquote.single-equip-cita {
			order: -1;
			background-size: auto;
			background-position: left 10px;
			padding-left: 68px;
			margin-bottom: 115px;
			margin-top: -10px;
		}

		.single-equip .single-equip-contingut {
			padding-left: 68px;
		}

	.equip-perfil {
		gap: 8px;
		margin-top: auto;
	}

		.equip-perfil li {
			border: var(--border);
			padding: 8px 17px 10px 20px;
			font-size: var(--txt-sm);
		}
		ul.equip-xxss a {
			width: 56px;
			height: 56px;
			border: 2.3px solid var(--Brand-Black, #3c3f36);
		}

		ul.equip-xxss a[href*="x.com"],ul.equip-xxss a[href*="linkedin"] {
			background-size: auto;
		}

	.single-equipo .bloc-relacionats {
		padding-top: var(--gapXXL);
	}

	/*** PPP - LLISTAT ÀREES - SERVEIS ***/

	.page-servicios {
		margin-top: -104px;
		padding-top: calc(var(--gap-page-top) + 104px);
	}

		.page-servicios .pagina-intro {
			padding-bottom: 160px;
		}

			:is(.page-servicios .pagina-intro)  > .wp-block-group__inner-container {
				align-items: start;
			}
			:is(.page-servicios .pagina-intro) h2 {
				font-size: var(--h5);
				line-height: var(--h5-lh);
				letter-spacing: var(--h5-ls);
				margin-bottom: var(--gapS);
				margin-top: var(--gapL);
			}

				:is(:is(.page-servicios .pagina-intro) h2)::after {
					width: 24px;
					height: 24px;
					top: 20px;
				}

				.desplegat:is(:is(.page-servicios .pagina-intro) h2)::after {
					background-position: center -109px;
				}

		.page-servicios .intro-pagina-txt > .wp-block-group__inner-container > p:first-of-type {
			margin-bottom: var(--gapXXL);
			font-size: var(--h5);
			line-height: var(--h5-lh);
			letter-spacing: var(--h5-ls);
		}

	.servicios-lista {
		padding: 140px 0 var(--gap-page-bottom) 0;
	}

		.servicios-lista h2 {
			margin-bottom: var(--gapXXL);
		}

		.servicios-lista ol {
			font-size: var(--h5);
			line-height: var(--h5-lh);
			letter-spacing: var(--h5-ls);
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: var(--gapXXL);
		}

			:is(.servicios-lista ol) li {
				padding-top: 9px;
				padding-bottom: 0;
			}

			:is(.servicios-lista ol) a {
				padding-right: 68px;
				padding-left: 103px;
				transition: padding-left 0.3s;
				background: url("../img/ico_arrow_diagonal.svg") no-repeat right 6px;
				background-size: auto;
			}

			:is(.servicios-lista ol) a:hover {
				text-decoration: none;
				padding-left: 143px;
				transition: padding-left 0.3s;
			}

	/**	PPP - SINGLE ÀREA **/
	.single-area-header {
		height: calc(100lvh - 106px);
	}

		.single-area-header .anchor_down a {
			width: 60px;
			height: 60px;
			bottom: 56px;
		}
		section[class*="area-bloc"] .wrap > .wp-block-group__inner-container {
			-moz-column-count: 2;
			     column-count: 2;
			-moz-column-gap: 88px;
			     column-gap: 88px;
			widows: 3;
			orphans: 3;
		}
			:is(section[class*="area-bloc"] .wrap > .wp-block-group__inner-container) h2 {
				margin-bottom: var(--gapXL);
			}

			:is(section[class*="area-bloc"] .wrap > .wp-block-group__inner-container) li::marker {
				font-size: 0.75em;
			}

	.area-bloc1 {
		padding: 120px 0;
		font-size: var(--txt-md);
		line-height: var(--txt-md-lh);
		letter-spacing: var(--txt-md-ls);
	}

	.area-bloc2 {
		padding: 104px 0 160px 0;
	}

	/**	PPP - ARTICLES I NOTÍCIES **/

	body[class*="page-actualidad"] .pagina-excerpt {
		margin-bottom: 120px;
	}
		:is(body[class*="page-actualidad"] .pagina-excerpt) p {
			margin: 0;
		}
		:is(.post-categories ul) a,:is(.post-categories ul) a:visited {
			border: var(--border);
			padding: 8px 20px;
			font-size: var(--txt-md);
			line-height: var(--txt-md-lh);
			letter-spacing: var(--txt-md-ls);
		}

	/* Teaser publicacions */
			:is(.teaser-article .post-categories ul) a,:is(.teaser-article .post-categories ul) a:visited {
				padding: 6px 20px;
				font-size: var(--txt-sm);
				line-height: var(--txt-sm-lh);
				letter-spacing: var(--txt-sm-ls);
			}

	/*** PPP - SINGLE NOTÍCIA / ARTICLE ***/

	.single-header {
		display: grid;
		grid-template-areas:
			"post-titol post-categories"
			"post-titol post-data";
		grid-template-columns: 7fr 3fr;
		-moz-column-gap: var(--gapL);
		     column-gap: var(--gapL);
		margin-top: var(--gapXXL);
		margin-bottom: 120px;
	}

		.single-header h1.post-titol {
			grid-area: post-titol;
			font-size: var(--h3);
			line-height: var(--h3-lh);
			letter-spacing: var(--h3-ls);
			margin-bottom: 0;
		}

		.single-header .post-categories {
			grid-area: post-categories;
			justify-self: end;
			margin-top: 18px;
		}

			:is(.single-header .post-categories) ul {
				justify-content: end;
			}

		.single-header .post-data {
			grid-area: post-data;
			justify-self: end;
			align-self: end;
			margin-bottom: 8px;
			margin-top: var(--gapS);
		}

	img.single-post-thumbnail {
		max-height: 400px;
	}

	.single-content {
		display: grid;
		grid-template-areas:
			"post-autor post-content"
			"post-aside post-content";
		grid-template-columns: 3fr 7fr;
		grid-template-rows: max-content 1fr;
		-moz-column-gap: var(--gapXS);
		     column-gap: var(--gapXS);
	}

		.single-content h2 {
			scroll-margin-top: 40px;
		}

			:is(.single-content h2):first-child {
				margin-top: 0;
			}

		.single-content .post-autores {
			margin-bottom: var(--gapXXL);
		}

		.single-content .post-autor {
			grid-area: post-autor;
			gap: 20px;
			font-size: var(--txt-md);
			line-height: var(--txt-md-lh);
			letter-spacing: var(--txt-md-ls);
			background-color: transparent;
			transition: background-color 0.3s;
		}

			:is(.single-content .post-autor):hover {
				background-color: var(--dark-grey);
				transition: background-color 0.3s;
			}

			:is(.single-content .post-autor):has(a[href=""]):hover {
				background-color: transparent;
			}

			:is(.single-content .post-autor) img {
				width: 80px;
				height: 80px;
			}

			:is(.single-content .post-autor) .post-autor-carrec {
				font-size: var(--txt-xxs);
				line-height: var(--txt-xxs-lh);
				letter-spacing: var(--txt-xxs-ls);
				border: var(--border-thin);
				padding: 3px 12px;
			}

		.single-content .post-aside {
			grid-area: post-aside;
			position: sticky;
			top: 50px;
			padding-right: 56px;
			align-self: start;
			display: block;
		}

			:is(.single-content .post-aside) h2 {
				display: block;
				font-size: var(--txt-lg);
				line-height: var(--txt-lg-lh);
				letter-spacing: var(--txt-lg-ls);
				padding-top: 0;
				padding-bottom: 6px;
				margin-bottom: var(--gapM);
			}

		.single-content .post-toc {
			position: static;
			background-color: transparent;
			border-radius: 0;
			padding: 0;
			margin-top: 0;
			margin-bottom: var(--gapXXL);
		}

			:is(.single-content .post-toc) p.post-toc-desplegable {
				display: none;
			}
				:is(:is(.single-content .post-toc) ol) li {
					grid-template-columns: 46px 1fr;
					margin-bottom: var(--gapS);
				}

					:is(:is(:is(.single-content .post-toc) ol) li)::before {
						width: 46px;
						height: 46px;
					}

		.single-content .recursos {
			order: 1;
			background-color: transparent;
			padding: 0;
		}

			:is(.single-content .recursos) h2 {
				display: block;
			}

			:is(.single-content .recursos) ul.recursos-list {
				grid-template-columns: 1fr;

				gap: 12px;
			}

			:is(.single-content .recursos) li {
				list-style: none;
				max-width: none;
			}

			:is(.single-content .recursos) .boto a {
				display: inline-flex;

				font-size: var(--txt-md);
				line-height: var(--txt-md-lh);
				letter-spacing: var(--txt-md-ls);
			}

		.single-content .post-content {
			grid-area: post-content;
			margin-top: 0;
		}

	/*** Pàgina bàsica amb títol ***/
	.page-template-page-with-title .single-content {
		display: block;
	}

	/*** PATRONS ***/

	/* Intro página */

	.pagina-intro {
		padding-bottom: var(--gapXXL);
	}

		.pagina-intro  > .wp-block-group__inner-container {
			display: grid;
			grid-template-columns: repeat(12, 1fr);
			gap: var(--gapXS);
			align-items: end;
		}

		.pagina-intro h2.intro-pagina-titol,.pagina-intro p.intro-pagina-titol {
			grid-column: 1/6;
			margin-bottom: 0;
		}

		.pagina-intro .intro-pagina-txt {
			grid-column: 7/-1;
		}

			:is(.pagina-intro .intro-pagina-txt)  > .wp-block-group__inner-container p {
				margin-bottom: 0;
			}

	/*** Footer ***/

	.footer-top {
		display: grid;
		grid-template-columns: 1fr 2fr;
		gap: var(--gapM);
	}

		.footer-top .logo-footer-desktop {
			max-width: 100%;
			margin-bottom: 0;
		}

	.footer-info-wrapper {
		justify-content: end;
	}

	/* Newsletter */
		:is(.newsletter .wrap) .newsletter-txt h2 {
			margin-bottom: 0;
		}

		:is(.newsletter .wrap) .mc-field-group {
			padding-top: 2px;
		}

		:is(.newsletter .wrap) input[type="email"] {
			font-size: var(--h6);
			letter-spacing: var(--h6-ls);
			padding-bottom: 16px;
		}

		:is(.newsletter .wrap) input[type="submit"] {
			width: 34px;
			height: 29px;
			background-size: contain;
			margin-top: 8px;
		}

		:is(.newsletter .wrap) .mailchimp-explain p {
			margin-bottom: 0.15em;
		}

	/*** Publicaciones ***/

	.grid-publicaciones {
		display: grid;
		grid-template-columns: repeat(12, 1fr);
	}
		.grid-publicaciones .grid-teasers {
			grid-column: 2/-2;
		}

	/*** PATRONS ***/

	/* Intro página */
		.pagina-intro h2.intro-pagina-titol,.pagina-intro p.intro-pagina-titol {
			grid-column: 1/7;
		}
		.pagina-intro .intro-pagina-txt {
			grid-column: 7/-1;
		}
}

@media (min-width: 1500px) {
	/*** Footer ***/

	.footer-top {
		grid-template-columns: 1fr 1fr;
	}

	.footer-info-wrapper {
		justify-content: space-between;
	}

	.grid-teasers.grid-equip {
		grid-template-columns: repeat(auto-fill, minmax(460px, 1fr));
	}
}

@media (min-width: 1700px) {
	#masthead .wrap {
		grid-template-columns: 280px 1fr 280px;
	}

	#site-navigation {
		justify-self: center;
	}
	.main-navigation ul {
		justify-content: center;
	}
}

@media (min-width: 1800px) {
	/* Grid teasers */

	.grid-teasers {
		grid-template-columns: repeat(4, 1fr);
	}
		:is(.bloc-relacionats .grid-teasers .teaser-article,.home .bloc-relacionats .grid-teasers .teaser-article,.single-post .bloc-relacionats .grid-teasers .teaser-article):nth-child(n + 4) {
			display: flex;
		}
}

@media (min-width: 1920px) {
	/* Carousel  home */

	.carousel-home-container .carousel-item .carousel-image {
		aspect-ratio: initial;
	}
		:is(.carousel-home-container .carousel-item .carousel-image) img {
			width: 100%;
			max-height: 820px;
		}
	.wrap {
		max-width: none;
	}

	.wrap10 {
		max-width: 1792px;
	}

	.wrap1000 {
		max-width: 1000px;
	}
}

/*# sourceMappingURL=style.css.map */