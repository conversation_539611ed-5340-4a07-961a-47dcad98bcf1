/* Navigation
--------------------------------------------- */

:root {
	interpolate-size: allow-keywords;
}
.main-navigation {
	display: block;
	width: 100%;
}
.main-navigation ul {
		list-style: none;
		margin: 0;
		padding-left: 0;
	}

/* Small menu. */

@media (max-width: 1280px) {
	#masthead.wrap {
		width: 100%; 
	}
		.main-navigation div[class*="menu-principal"] {
			display: none;
			z-index: 2;
		}

			:is(.main-navigation div[class*="menu-principal"]) a {
				font-size: var(--h5);
				letter-spacing: var(--h5-ls);
				display: block;
				text-align: center;
				padding: 8px 8px 10px 8px;
				margin-bottom: 30px;
			}

		.main-navigation  + .wpml-ls {
			display: none;
		}
			.main-navigation.toggled div[class*="menu-principal"] {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}

			.main-navigation.toggled  + .wpml-ls.wpml-ls-legacy-list-horizontal {
				padding: 0;
				display: block;
				align-self: center;
				z-index: 5;
				border: 1px solid var(--Brand-Black);
				border-radius: 50px;
			}
				:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) ul {
					display: flex;
					justify-content: center;
					font-size: var(--txt-xl);
				}

					:is(:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) ul)  > li:not(.wpml-ls-current-language) {
						opacity: 1;
						visibility: visible;
						position: static;
						transform: none;
					}

					:is(:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) ul)  > li[class*="wpml-ls-item-es"] {
						order: 1;
					}

					:is(:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) ul)  > li[class*="wpml-ls-item-ca"] {
						order: 2;
					}

					:is(:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) ul)  > li[class*="wpml-ls-item-en"] {
						order: 3;
					}

				:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) a {
					display: block;
					padding: 8px 20px;
				}

				:is(.main-navigation.toggled + .wpml-ls.wpml-ls-legacy-list-horizontal) li.wpml-ls-current-language a {
					background-color: var(--Brand-Black);
					border-radius: 50px;
					color: var(--yellow);
				}

	/* Botó toggle */

	.menu-toggle,
	.main-navigation.toggled ul {
		display: block;
	}

	button.menu-toggle {
		background: none;
		border: none;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 36px;
		height: 25px;
		position: relative;
		text-indent: -999em;
		z-index: 4;
		position: absolute;
		top: 36px;
		right: 6px;
	}

	button.menu-toggle span {
		background-color: #000;
		display: block;
		height: 3px;
		width: 100%;
		position: absolute;
		transition: all 0.3s ease-in-out;
	}

	button.menu-toggle span:nth-child(1) {
		top: 0;
	}

	button.menu-toggle span:nth-child(2) {
		top: 50%;
		transform: translateY(-50%);
	}

	button.menu-toggle span:nth-child(3) {
		bottom: 0;
	}

	.toggled button.menu-toggle span:nth-child(1) {
		transform: translateY(11px) rotate(45deg);
	}

	.toggled button.menu-toggle span:nth-child(2) {
		opacity: 0;
	}

	.toggled button.menu-toggle span:nth-child(3) {
		transform: translateY(-11px) rotate(-45deg);
	}
}

@media (min-width: 1400px) {
	.menu-toggle {
		display: none;
	}

	.main-navigation ul {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 36px;
		mix-blend-mode: multiply;
	}

		:is(.main-navigation ul) a,:is(.main-navigation ul) a:visited {
			font-size: var(--txt-md);
			letter-spacing: var(--txt-md-ls);
			line-height: 1.3;
			padding: 8px;
			border: 1.5px solid transparent;
			border-radius: 60px;
			color: var(--Brand-Black);
			transition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;
		}

		:is(.main-navigation ul) a:hover {
			text-decoration: none;
			border-color: var(--Brand-Black);
			padding: 8px 16px;
			background-color: var(--Brand-Black);
			color: var(--bgpage);
			transition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;
		}

		#primary-menu:is(.main-navigation ul) > li[class*="current"] > a {
			padding: 8px 16px;
			border-color: var(--Brand-Black);
		}

	.single-post .menu a[href*="actuali"],
	.single-equipo .menu a[href*="equip"],
	.single-categoria_terraqui_a .menu a[href*="serv"] {
		border-color: var(--Brand-Black);
		transition: border-color 0.3s;
		padding: 8px 16px;
	}

	.wpml-ls.wpml-ls-legacy-list-horizontal {
		padding: 0;
		justify-self: end;
		gap: 0;
		transition: all 0.3s;
	}

		.wpml-ls.wpml-ls-legacy-list-horizontal  > ul {
			display: flex;
			align-items: center;
			width: auto;
		}

			:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul)  > li:not(.wpml-ls-current-language) {
				opacity: 0;
				visibility: visible;
				position: static;
				transform: none;
				width: 0;
				transition: opacity 0.3s, width 0.3s;
			}

				:is(:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul) > li:not(.wpml-ls-current-language)) a {
					color: var(--Brand-Black-50);
					transition: color 0.3s;
				}
					:is(:is(:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul) > li:not(.wpml-ls-current-language)) a):hover {
						color: var(--Brand-Black);
						transition: color 0.3s;
					}

			:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul)  > li > a {
				white-space: nowrap;
				padding: 0;
			}

			:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul)  > li > a > span {
				vertical-align: unset;
				display: inline-block;
			}

			:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul)  > li.wpml-ls-current-language > a {
				padding-right: 7px;
				display: flex;
				gap: 9px;
				align-items: center;
			}

			:is(.wpml-ls.wpml-ls-legacy-list-horizontal > ul)  > li.wpml-ls-current-language > a::after {
				content: "";
				width: 0.5em;
				height: 0.5em;
				border-top: 1.5px solid var(--Brand-Black);
				border-right: 1.5px solid var(--Brand-Black);
				display: flex;
				justify-content: center;
				align-items: center;
				transform: rotate(45deg);
				transform-origin: center;
				transition: all 0.3s;
			}

		.wpml-ls.wpml-ls-legacy-list-horizontal.wpml-ls-expanded > ul {
			gap: 10px;
			transition: all 0.3s;
		}
			:is(.wpml-ls.wpml-ls-legacy-list-horizontal.wpml-ls-expanded > ul)  > li:not(.wpml-ls-current-language) {
				width: auto;
				opacity: 1;
				transform: width 0.3s;
			}
			:is(.wpml-ls.wpml-ls-legacy-list-horizontal.wpml-ls-expanded > ul)  > li.wpml-ls-current-language {
				transform: none;
			}

				:is(:is(.wpml-ls.wpml-ls-legacy-list-horizontal.wpml-ls-expanded > ul) > li.wpml-ls-current-language)  > a::after {
					transform: rotate(225deg);
					transition: all 0.3s;
					position: relative;

					left: 5px;
				}
}

/*# sourceMappingURL=style_menu.css.map */