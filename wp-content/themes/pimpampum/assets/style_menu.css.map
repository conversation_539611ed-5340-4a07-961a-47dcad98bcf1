{"version": 3, "sources": ["../css/style_menu.css"], "names": [], "mappings": "AAAA;+CAC+C;;AAE/C;CACC,gCAAgC;AACjC;AACA;CACC,cAAc;CACd,WAAW;AAMZ;AALC;EACC,gBAAgB;EAChB,SAAS;EACT,eAAe;CAChB;;AAGD,gBAAgB;;AAEhB;CACC;EACC,WAAW;CACZ;EAEC;GACC,aAAa;GACb,UAAU;EAUX;;GARC;IACC,oBAAoB;IACpB,4BAA4B;IAC5B,cAAc;IACd,kBAAkB;IAClB,yBAAyB;IACzB,mBAAmB;GACpB;;EAGD;GACC,aAAa;EACd;GAGC;IACC,aAAa;IACb,sBAAsB;IACtB,uBAAuB;IACvB,mBAAmB;GACpB;;GAEA;IACC,UAAU;IACV,cAAc;IACd,kBAAkB;IAClB,UAAU;IACV,oCAAoC;IACpC,mBAAmB;GAoCpB;IAnCC;KACC,aAAa;KACb,uBAAuB;KACvB,wBAAwB;IAoBzB;;KAlBC;MACC,UAAU;MACV,mBAAmB;MACnB,gBAAgB;MAChB,eAAe;KAChB;;KAEA;MACC,QAAQ;KACT;;KAEA;MACC,QAAQ;KACT;;KAEA;MACC,QAAQ;KACT;;IAGD;KACC,cAAc;KACd,iBAAiB;IAClB;;IAEA;KACC,oCAAoC;KACpC,mBAAmB;KACnB,oBAAoB;IACrB;;CAKH,gBAAgB;;CAEhB;;EAEC,cAAc;CACf;;CAEA;EACC,gBAAgB;EAChB,YAAY;EACZ,eAAe;EACf,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,UAAU;EACV,kBAAkB;EAClB,SAAS;EACT,UAAU;CACX;;CAEA;EACC,sBAAsB;EACtB,cAAc;EACd,WAAW;EACX,WAAW;EACX,kBAAkB;EAClB,gCAAgC;CACjC;;CAEA;EACC,MAAM;CACP;;CAEA;EACC,QAAQ;EACR,2BAA2B;CAC5B;;CAEA;EACC,SAAS;CACV;;CAEA;EACC,yCAAyC;CAC1C;;CAEA;EACC,UAAU;CACX;;CAEA;EACC,2CAA2C;CAC5C;AACD;;AAEA;CACC;EACC,aAAa;CACd;;CAEA;EACC,aAAa;EACb,yBAAyB;EACzB,mBAAmB;EACnB,SAAS;EACT,wBAAwB;CA2BzB;;EAzBC;GAEC,wBAAwB;GACxB,gCAAgC;GAChC,gBAAgB;GAChB,YAAY;GACZ,+BAA+B;GAC/B,mBAAmB;GACnB,yBAAyB;GACzB,8EAA8E;EAC/E;;EAEA;GACC,qBAAqB;GACrB,gCAAgC;GAChC,iBAAiB;GACjB,oCAAoC;GACpC,oBAAoB;GACpB,8EAA8E;EAC/E;;EAEA;GACC,iBAAiB;GACjB,gCAAgC;EACjC;;CAGD;;;EAGC,gCAAgC;EAChC,6BAA6B;EAC7B,iBAAiB;CAClB;;CAEA;EACC,UAAU;EACV,iBAAiB;EACjB,MAAM;EACN,oBAAoB;CA6ErB;;EA3EC;GACC,aAAa;GACb,mBAAmB;GACnB,WAAW;EAkDZ;;GAhDC;IACC,UAAU;IACV,mBAAmB;IACnB,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,oCAAoC;GAUrC;;IARC;KACC,4BAA4B;KAC5B,sBAAsB;IAKvB;KAJC;MACC,yBAAyB;MACzB,sBAAsB;KACvB;;GAIF;IACC,mBAAmB;IACnB,UAAU;GACX;;GAEA;IACC,qBAAqB;IACrB,qBAAqB;GACtB;;GAEA;IACC,kBAAkB;IAClB,aAAa;IACb,QAAQ;IACR,mBAAmB;GACpB;;GAEA;IACC,WAAW;IACX,YAAY;IACZ,aAAa;IACb,0CAA0C;IAC1C,4CAA4C;IAC5C,aAAa;IACb,uBAAuB;IACvB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,oBAAoB;GACrB;;EAGD;GACC,SAAS;GACT,oBAAoB;EAiBrB;GAhBC;IACC,WAAW;IACX,UAAU;IACV,qBAAqB;GACtB;GACA;IACC,eAAe;GAShB;;IAPC;KACC,yBAAyB;KACzB,oBAAoB;KACpB,kBAAkB;;KAElB,SAAS;IACV;AAIJ", "file": "style_menu.css", "sourcesContent": ["/* Navigation\n--------------------------------------------- */\n\n:root {\n\tinterpolate-size: allow-keywords;\n}\n.main-navigation {\n\tdisplay: block;\n\twidth: 100%;\n\tul {\n\t\tlist-style: none;\n\t\tmargin: 0;\n\t\tpadding-left: 0;\n\t}\n}\n\n/* Small menu. */\n\n@media (max-width: 1279px) {\n\t#masthead.wrap {\n\t\twidth: 100%;\n\t}\n\t.main-navigation {\n\t\tdiv[class*=\"menu-principal\"] {\n\t\t\tdisplay: none;\n\t\t\tz-index: 2;\n\n\t\t\ta {\n\t\t\t\tfont-size: var(--h5);\n\t\t\t\tletter-spacing: var(--h5-ls);\n\t\t\t\tdisplay: block;\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 8px 8px 10px 8px;\n\t\t\t\tmargin-bottom: 30px;\n\t\t\t}\n\t\t}\n\n\t\t+ .wpml-ls {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t&.toggled {\n\t\t\tdiv[class*=\"menu-principal\"] {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t+ .wpml-ls.wpml-ls-legacy-list-horizontal {\n\t\t\t\tpadding: 0;\n\t\t\t\tdisplay: block;\n\t\t\t\talign-self: center;\n\t\t\t\tz-index: 5;\n\t\t\t\tborder: 1px solid var(--Brand-Black);\n\t\t\t\tborder-radius: 50px;\n\t\t\t\tul {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tfont-size: var(--txt-xl);\n\n\t\t\t\t\t> li:not(.wpml-ls-current-language) {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\tvisibility: visible;\n\t\t\t\t\t\tposition: static;\n\t\t\t\t\t\ttransform: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t> li[class*=\"wpml-ls-item-es\"] {\n\t\t\t\t\t\torder: 1;\n\t\t\t\t\t}\n\n\t\t\t\t\t> li[class*=\"wpml-ls-item-ca\"] {\n\t\t\t\t\t\torder: 2;\n\t\t\t\t\t}\n\n\t\t\t\t\t> li[class*=\"wpml-ls-item-en\"] {\n\t\t\t\t\t\torder: 3;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\ta {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tpadding: 8px 20px;\n\t\t\t\t}\n\n\t\t\t\tli.wpml-ls-current-language a {\n\t\t\t\t\tbackground-color: var(--Brand-Black);\n\t\t\t\t\tborder-radius: 50px;\n\t\t\t\t\tcolor: var(--yellow);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Botó toggle */\n\n\t.menu-toggle,\n\t.main-navigation.toggled ul {\n\t\tdisplay: block;\n\t}\n\n\tbutton.menu-toggle {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: 36px;\n\t\theight: 25px;\n\t\tposition: relative;\n\t\ttext-indent: -999em;\n\t\tz-index: 4;\n\t\tposition: absolute;\n\t\ttop: 36px;\n\t\tright: 6px;\n\t}\n\n\tbutton.menu-toggle span {\n\t\tbackground-color: #000;\n\t\tdisplay: block;\n\t\theight: 3px;\n\t\twidth: 100%;\n\t\tposition: absolute;\n\t\ttransition: all 0.3s ease-in-out;\n\t}\n\n\tbutton.menu-toggle span:nth-child(1) {\n\t\ttop: 0;\n\t}\n\n\tbutton.menu-toggle span:nth-child(2) {\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t}\n\n\tbutton.menu-toggle span:nth-child(3) {\n\t\tbottom: 0;\n\t}\n\n\t.toggled button.menu-toggle span:nth-child(1) {\n\t\ttransform: translateY(11px) rotate(45deg);\n\t}\n\n\t.toggled button.menu-toggle span:nth-child(2) {\n\t\topacity: 0;\n\t}\n\n\t.toggled button.menu-toggle span:nth-child(3) {\n\t\ttransform: translateY(-11px) rotate(-45deg);\n\t}\n}\n\n@media (min-width: 1280px) {\n\t.menu-toggle {\n\t\tdisplay: none;\n\t}\n\n\t.main-navigation ul {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\tgap: 36px;\n\t\tmix-blend-mode: multiply;\n\n\t\ta,\n\t\ta:visited {\n\t\t\tfont-size: var(--txt-md);\n\t\t\tletter-spacing: var(--txt-md-ls);\n\t\t\tline-height: 1.3;\n\t\t\tpadding: 8px;\n\t\t\tborder: 1.5px solid transparent;\n\t\t\tborder-radius: 60px;\n\t\t\tcolor: var(--Brand-Black);\n\t\t\ttransition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;\n\t\t}\n\n\t\ta:hover {\n\t\t\ttext-decoration: none;\n\t\t\tborder-color: var(--Brand-Black);\n\t\t\tpadding: 8px 16px;\n\t\t\tbackground-color: var(--Brand-Black);\n\t\t\tcolor: var(--bgpage);\n\t\t\ttransition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;\n\t\t}\n\n\t\t&#primary-menu > li[class*=\"current\"] > a {\n\t\t\tpadding: 8px 16px;\n\t\t\tborder-color: var(--Brand-Black);\n\t\t}\n\t}\n\n\t.single-post .menu a[href*=\"actuali\"],\n\t.single-equipo .menu a[href*=\"equip\"],\n\t.single-categoria_terraqui_a .menu a[href*=\"serv\"] {\n\t\tborder-color: var(--Brand-Black);\n\t\ttransition: border-color 0.3s;\n\t\tpadding: 8px 16px;\n\t}\n\n\t.wpml-ls.wpml-ls-legacy-list-horizontal {\n\t\tpadding: 0;\n\t\tjustify-self: end;\n\t\tgap: 0;\n\t\ttransition: all 0.3s;\n\n\t\t> ul {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\twidth: auto;\n\n\t\t\t> li:not(.wpml-ls-current-language) {\n\t\t\t\topacity: 0;\n\t\t\t\tvisibility: visible;\n\t\t\t\tposition: static;\n\t\t\t\ttransform: none;\n\t\t\t\twidth: 0;\n\t\t\t\ttransition: opacity 0.3s, width 0.3s;\n\n\t\t\t\ta {\n\t\t\t\t\tcolor: var(--Brand-Black-50);\n\t\t\t\t\ttransition: color 0.3s;\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: var(--Brand-Black);\n\t\t\t\t\t\ttransition: color 0.3s;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t> li > a {\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t> li > a > span {\n\t\t\t\tvertical-align: unset;\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\n\t\t\t> li.wpml-ls-current-language > a {\n\t\t\t\tpadding-right: 7px;\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 9px;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t> li.wpml-ls-current-language > a::after {\n\t\t\t\tcontent: \"\";\n\t\t\t\twidth: 0.5em;\n\t\t\t\theight: 0.5em;\n\t\t\t\tborder-top: 1.5px solid var(--Brand-Black);\n\t\t\t\tborder-right: 1.5px solid var(--Brand-Black);\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\ttransform: rotate(45deg);\n\t\t\t\ttransform-origin: center;\n\t\t\t\ttransition: all 0.3s;\n\t\t\t}\n\t\t}\n\n\t\t&.wpml-ls-expanded > ul {\n\t\t\tgap: 10px;\n\t\t\ttransition: all 0.3s;\n\t\t\t> li:not(.wpml-ls-current-language) {\n\t\t\t\twidth: auto;\n\t\t\t\topacity: 1;\n\t\t\t\ttransform: width 0.3s;\n\t\t\t}\n\t\t\t> li.wpml-ls-current-language {\n\t\t\t\ttransform: none;\n\n\t\t\t\t> a::after {\n\t\t\t\t\ttransform: rotate(225deg);\n\t\t\t\t\ttransition: all 0.3s;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\tleft: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n"]}