/* Navigation
--------------------------------------------- */

:root {
	interpolate-size: allow-keywords;
}
.main-navigation {
	display: block;
	width: 100%;
	ul {
		list-style: none;
		margin: 0;
		padding-left: 0;
	}
}

/* Small menu. */

@media (max-width: 1280px) {
	#masthead.wrap {
		width: 100%; 
	}
	.main-navigation {
		div[class*="menu-principal"] {
			display: none;
			z-index: 2;

			a {
				font-size: var(--h5);
				letter-spacing: var(--h5-ls);
				display: block;
				text-align: center;
				padding: 8px 8px 10px 8px;
				margin-bottom: 30px;
			}
		}

		+ .wpml-ls {
			display: none;
		}

		&.toggled {
			div[class*="menu-principal"] {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}

			+ .wpml-ls.wpml-ls-legacy-list-horizontal {
				padding: 0;
				display: block;
				align-self: center;
				z-index: 5;
				border: 1px solid var(--Brand-Black);
				border-radius: 50px;
				ul {
					display: flex;
					justify-content: center;
					font-size: var(--txt-xl);

					> li:not(.wpml-ls-current-language) {
						opacity: 1;
						visibility: visible;
						position: static;
						transform: none;
					}

					> li[class*="wpml-ls-item-es"] {
						order: 1;
					}

					> li[class*="wpml-ls-item-ca"] {
						order: 2;
					}

					> li[class*="wpml-ls-item-en"] {
						order: 3;
					}
				}

				a {
					display: block;
					padding: 8px 20px;
				}

				li.wpml-ls-current-language a {
					background-color: var(--Brand-Black);
					border-radius: 50px;
					color: var(--yellow);
				}
			}
		}
	}

	/* Botó toggle */

	.menu-toggle,
	.main-navigation.toggled ul {
		display: block;
	}

	button.menu-toggle {
		background: none;
		border: none;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 36px;
		height: 25px;
		position: relative;
		text-indent: -999em;
		z-index: 4;
		position: absolute;
		top: 36px;
		right: 6px;
	}

	button.menu-toggle span {
		background-color: #000;
		display: block;
		height: 3px;
		width: 100%;
		position: absolute;
		transition: all 0.3s ease-in-out;
	}

	button.menu-toggle span:nth-child(1) {
		top: 0;
	}

	button.menu-toggle span:nth-child(2) {
		top: 50%;
		transform: translateY(-50%);
	}

	button.menu-toggle span:nth-child(3) {
		bottom: 0;
	}

	.toggled button.menu-toggle span:nth-child(1) {
		transform: translateY(11px) rotate(45deg);
	}

	.toggled button.menu-toggle span:nth-child(2) {
		opacity: 0;
	}

	.toggled button.menu-toggle span:nth-child(3) {
		transform: translateY(-11px) rotate(-45deg);
	}
}

@media (min-width: 1400px) {
	.menu-toggle {
		display: none;
	}

	.main-navigation ul {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 36px;
		mix-blend-mode: multiply;

		a,
		a:visited {
			font-size: var(--txt-md);
			letter-spacing: var(--txt-md-ls);
			line-height: 1.3;
			padding: 8px;
			border: 1.5px solid transparent;
			border-radius: 60px;
			color: var(--Brand-Black);
			transition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;
		}

		a:hover {
			text-decoration: none;
			border-color: var(--Brand-Black);
			padding: 8px 16px;
			background-color: var(--Brand-Black);
			color: var(--bgpage);
			transition: padding 0.3s, border-color 0.2s, color 0.3s, background-color 0.3s;
		}

		&#primary-menu > li[class*="current"] > a {
			padding: 8px 16px;
			border-color: var(--Brand-Black);
		}
	}

	.single-post .menu a[href*="actuali"],
	.single-equipo .menu a[href*="equip"],
	.single-categoria_terraqui_a .menu a[href*="serv"] {
		border-color: var(--Brand-Black);
		transition: border-color 0.3s;
		padding: 8px 16px;
	}

	.wpml-ls.wpml-ls-legacy-list-horizontal {
		padding: 0;
		justify-self: end;
		gap: 0;
		transition: all 0.3s;

		> ul {
			display: flex;
			align-items: center;
			width: auto;

			> li:not(.wpml-ls-current-language) {
				opacity: 0;
				visibility: visible;
				position: static;
				transform: none;
				width: 0;
				transition: opacity 0.3s, width 0.3s;

				a {
					color: var(--Brand-Black-50);
					transition: color 0.3s;
					&:hover {
						color: var(--Brand-Black);
						transition: color 0.3s;
					}
				}
			}

			> li > a {
				white-space: nowrap;
				padding: 0;
			}

			> li > a > span {
				vertical-align: unset;
				display: inline-block;
			}

			> li.wpml-ls-current-language > a {
				padding-right: 7px;
				display: flex;
				gap: 9px;
				align-items: center;
			}

			> li.wpml-ls-current-language > a::after {
				content: "";
				width: 0.5em;
				height: 0.5em;
				border-top: 1.5px solid var(--Brand-Black);
				border-right: 1.5px solid var(--Brand-Black);
				display: flex;
				justify-content: center;
				align-items: center;
				transform: rotate(45deg);
				transform-origin: center;
				transition: all 0.3s;
			}
		}

		&.wpml-ls-expanded > ul {
			gap: 10px;
			transition: all 0.3s;
			> li:not(.wpml-ls-current-language) {
				width: auto;
				opacity: 1;
				transform: width 0.3s;
			}
			> li.wpml-ls-current-language {
				transform: none;

				> a::after {
					transform: rotate(225deg);
					transition: all 0.3s;
					position: relative;

					left: 5px;
				}
			}
		}
	}
}
