jQuery(document).ready(function($) {
    var $carousel = $('.carousel-home');
    var isMobile = $(window).width() <= 1280;

    $carousel.on('init reInit afterChange', function(event, slick, currentSlide, nextSlide){
        var i = (currentSlide ? currentSlide : 0) + 1;
        $('.slide-count').text(i + '/' + slick.slideCount);
    });

    function initCarousel() {
        isMobile = $(window).width() <= 1280;

        $carousel.slick({
            autoplay: true,
            autoplaySpeed: 5000,
            dots: isMobile, // Show dots only on mobile
            arrows: true,
            infinite: true,
            speed: 800,
            slidesToShow: 1,
            adaptiveHeight: true,
            fade: true,
            cssEase: 'linear'
        });
    }

    // Initialize carousel
    initCarousel();

    // Reinitialize on window resize
    $(window).on('resize', function() {
        var newIsMobile = $(window).width() <= 1280;

        // Only reinitialize if mobile state changed
        if (newIsMobile !== isMobile) {
            $carousel.slick('unslick');
            initCarousel();
        }
    });
});