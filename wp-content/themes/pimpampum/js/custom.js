
const BREAKPOINT=768
let sliders=[];

jQuery(document).ready(function($) {

  const lang = $('html').attr('lang').substring(0, 2);
  const base_url = window.location.origin;
// Add sticky header functionality
let lastScrollTop = 0; // Track the previous scroll position
const scrollThreshold = 0; // Minimum scroll before sticky behavior activates

$(window).scroll(function() {
  const scrollTop = $(window).scrollTop();
  const header = $('#masthead');
  
  // Check if we're scrolled past the threshold point
  if (scrollTop > scrollThreshold) {
    // Determine scroll direction
    if (scrollTop < lastScrollTop) {
      // Scrolling UP - add sticky class
      header.addClass('sticky');
    } else {
      // Scrolling DOWN - remove sticky class
      header.removeClass('sticky');
    }
  } else {
    // Not scrolled enough - remove s<wticky class
    header.removeClass('sticky');
  }
  
  // Update the last scroll position
  lastScrollTop = scrollTop;
});

  //hack temporal per klaro 


  /*
  setTimeout(function(){
  if(lang=="ca"){
   
    $('.cm-header a').attr('href',config.basePath+'/ca/cookies');
  }
  if(lang=="es"){

    $('.cm-header a').attr('href',config.basePath+'/cookies');
  }
  if(lang=="en"){
    $('.cm-header a').attr('href',config.basePath+'/en/cookies');
  }
  }, 1000);
*/

/* esborrar
$('.categoria-terraqui-terms').on('wheel DOMMouseScroll', function(e) {

  // If this element has scrollable content
  if (this.scrollHeight > this.clientHeight) {
    // Get current scroll position
    const scrollTop = this.scrollTop;
    const scrollHeight = this.scrollHeight;
    const height = this.clientHeight;
    const delta = e.originalEvent.deltaY;
    
    // Prevent the event from bubbling to parent elements
    e.stopPropagation();
    
    // Check if scroll is at the top or bottom
    const isAtTop = scrollTop === 0;
    const isAtBottom = scrollTop + height >= scrollHeight;
    
    // If scrolling up and at the top, or scrolling down and at the bottom,
    // don't prevent default - let the page scroll
    if ((delta < 0 && isAtTop) || (delta > 0 && isAtBottom)) {
      return;
    }


    
    // Otherwise, prevent default and handle the scroll ourselves
    e.preventDefault();
    this.scrollTop += delta*2;
  }
});
*/
    // Hide all items except the first one

    /*
    var otherLanguages = jQuery(".wpml-ls-statics-shortcode_actions ul li:not(:first-child)");
    otherLanguages.hide();
    
    // Make the first item clickable
    jQuery(".wpml-ls-statics-shortcode_actions ul li:first-child").css("cursor", "pointer").click(function(e) {
      e.preventDefault();
      
      // Use simpler animation methods instead of .show("slide")
      otherLanguages.css({
        "display": "inline-block", 
        "opacity": "0", 
        "margin-left": "-50px"
      }).animate({
        "opacity": "1",
        "margin-left": "0"
      }, 300);
    });
    */
    
    if ($('body.page-template-page-servicios-php').length > 0) {
      $('h2').next('p').hide();
      $('h2').css("cursor", "pointer").off('click').click(function() {
        $(this).next('p').slideToggle();
        $(this).toggleClass('desplegat');
      });
    }

    function updateMobile(){
      if ($(window).width() <= BREAKPOINT) { // Check if the screen width is less than or equal to 768px (mobile)


        //carousel noticies home
        if ($("body.home").length > 0 && !$(".grid-teasers").hasClass("tns-initialized")) {
          console.log("creant");

          let slider=tns({
            container: ".grid-teasers",
          //  slideBy: "page",
            loop: false,
            //center: true,
            controls: false,
            autoplay: false,
            items: 1.2, 
            mouseDrag: true,
            arrowKeys: true,
            //fixedWidth: 287,
            gutter: 22,
        
          });

          $(".grid-teasers").addClass("tns-initialized");
          sliders.push(slider);
        }
        

        // Only add the event listener if it hasn't been added before
        $('.js-desplega-mobile a').off('click').on('click', function(ev) {
          ev.stopPropagation(); // Prevent bubbling to parent

          var target = $(this.hash);
          if (target.length) {
          /*
            $('html, body').animate({
              scrollTop: target.offset().top - 0 //400
            }, 400);
            */
            $('.js-desplega-mobile').removeClass('desplegat');
          }
          $('.js-desplega-mobile-content').hide();
        });
        $('.js-desplega-mobile-content').hide();

        $('.js-desplega-mobile-content a').off('click').on('click', function(ev) {
          ev.stopPropagation(); // Prevent bubbling to parent
          $('.js-desplega-mobile').removeClass('desplegat');
          $('.js-desplega-mobile-content').hide();
        });

        $('.js-desplega-mobile').off('click').on('click', function(ev) {
          // Only toggle if the click target is not a link or inside the content area
          if ($(ev.target).closest('a, .js-desplega-mobile-content').length === 0) {
            $(this).toggleClass('desplegat');
            $(this).find('.js-desplega-mobile-content').slideToggle();
          }
        });
        


       
        
        
        if ($('.ver-mas').length > 0) {
    
          var verMas = $('.ver-mas');
          var followingItems = verMas.nextAll('p');
          if(followingItems.length>0){
            followingItems.hide();
            
            const txt="Leer más";
            if(lang=="ca") txt="Veure més";
            if(lang=="en") txt="View more";
            if($('.ver-mas-link').length==0){
              var verMasLink = $('<a href="#" class="ver-mas-link">'+txt+'</a>');
              verMas.append(verMasLink);
              $('.ver-mas-link').on("click",function(e) {
              
                e.preventDefault();
                followingItems.slideToggle();
                $('.ver-mas-link').remove();
              });
            }
          }
          
        
        }

    }else {
      //desktop
      if(sliders.length>0){
        sliders.forEach(function(slider){
          slider.destroy();
        });
        sliders=[];
      }
    }


    }
    updateMobile();
    window.addEventListener('resize', updateMobile);


  });







jQuery(document).ready(function($) {
// Add toggle functionality for filter dropdowns
$('.js-filtre-desplegable .filtre-area-header').on('click', function(e) {
  var $parent = $(this).parent();
  $parent.toggleClass('desplegat');
  $parent.find('.filtre-area-content').slideToggle();
});

// Close filter dropdowns when clicking outside
$(document).on('click', function(e) {
  // Don't close if the click was inside a filter dropdown area
  if ($(e.target).closest('.js-filtre-desplegable').length === 0) {
    // Click was outside, close all open dropdowns
    $('.js-filtre-desplegable.desplegat').removeClass('desplegat');
    $('.filtre-area-content').slideUp();
  }
});

$('.js-tipo').on('click', function(e) {
  e.preventDefault();
  var slug = $(this).data('slug');
filters.tipo_de_entrada = slug;

refreshFilters();


});

$('.filtre-eliminar').on('click', function(e) {
  e.preventDefault();
  var slug = $(this).data('slug');

var params = filters.categoria_terraqui;
  params = params.filter(function(value) {
      return value !== slug;
  });
  filters.categoria_terraqui = params;
refreshFilters();
});


$('.filtre-recent').on('click', function(e) {
e.preventDefault();

if(filters.order=="asc"){
  filters.order="desc";
  $('.filtre-recent a').text(label_reciente)
}else{
  filters.order="asc";
  $('.filtre-recent a').text(label_antiguo)
}

refreshFilters();
});



$('.search-submit').on('click', function(e) {

  e.preventDefault();
  filters.search = $('.search-field').val();

  console.log(filters);
  refreshFilters();
});


});

function add_filter(slug) {
  // Keep for backward compatibility
  filters.categoria_terraqui.push(slug);
  console.log("filters", filters);	
  refreshFilters();
}

function toggle_filter(slug, checked) {
  if (checked) {
    // Add the category if it's not already in the array
    if (!filters.categoria_terraqui.includes(slug)) {
      filters.categoria_terraqui.push(slug);
    }
  } else {
    // Remove the category if it's checked off
    filters.categoria_terraqui = filters.categoria_terraqui.filter(function(value) {
      return value !== slug;
    });
  }
  console.log("filters updated:", filters);
}

function apply_filters() {
  refreshFilters();
}

function refreshFilters() {
var url = new URL(window.location.href);

// Clear existing filter params
url.searchParams.delete('categoria_terraqui');
url.searchParams.delete('tipo_de_entrada');
url.searchParams.delete('search');
// Add current filters to the URL
if (filters.categoria_terraqui.length > 0) {
  url.searchParams.set('categoria_terraqui', filters.categoria_terraqui.join(','));
}
if (filters.tipo_de_entrada) {
  url.searchParams.set('tipo_de_entrada', filters.tipo_de_entrada);
}
if (filters.order) {
  url.searchParams.set('order', filters.order);
}
if(filters.search){
  url.searchParams.set('search', filters.search);
}
console.log("url", url.toString());
// Reload the page with the new URL
window.location.href = url.toString();
}
